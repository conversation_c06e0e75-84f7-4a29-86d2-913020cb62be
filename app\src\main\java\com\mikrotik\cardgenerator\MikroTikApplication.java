package com.mikrotik.cardgenerator;

import android.app.Application;
import android.content.Context;
import android.content.SharedPreferences;
import android.os.Environment;
import androidx.room.Room;
import com.mikrotik.cardgenerator.data.database.AppDatabase;
import com.mikrotik.cardgenerator.utils.EncryptionUtils;
import com.mikrotik.cardgenerator.utils.LoggingUtils;
import timber.log.Timber;
import java.io.File;

/**
 * كلاس التطبيق الرئيسي - مولد كروت MikroTik
 * يحتوي على إعدادات التطبيق الأساسية وقاعدة البيانات
 */
public class MikroTikApplication extends Application {

    private static MikroTikApplication instance;
    private AppDatabase database;
    private SharedPreferences preferences;
    private EncryptionUtils encryptionUtils;

    // مجلدات التطبيق
    public static final String APP_FOLDER = "MikroTikCardGenerator";
    public static final String EXPORTS_FOLDER = "exports";
    public static final String BACKUPS_FOLDER = "backups";
    public static final String TEMPLATES_FOLDER = "templates";
    public static final String LOGS_FOLDER = "logs";

    @Override
    public void onCreate() {
        super.onCreate();
        instance = this;

        // إعداد نظام السجلات
        setupLogging();

        // إعداد قاعدة البيانات
        setupDatabase();

        // إعداد التشفير
        setupEncryption();

        // إعداد المجلدات
        setupDirectories();

        // إعداد الإعدادات المشتركة
        setupPreferences();

        Timber.i("تم بدء تشغيل تطبيق مولد كروت MikroTik");
    }

    /**
     * الحصول على مثيل التطبيق
     */
    public static MikroTikApplication getInstance() {
        return instance;
    }

    /**
     * إعداد نظام السجلات
     */
    private void setupLogging() {
        // إعداد نظام السجلات للتطوير والإنتاج
        try {
            Timber.plant(new Timber.DebugTree());
        } catch (Exception e) {
            Timber.plant(new LoggingUtils.ReleaseTree());
        }
    }

    /**
     * إعداد قاعدة البيانات
     */
    private void setupDatabase() {
        database = Room.databaseBuilder(
                getApplicationContext(),
                AppDatabase.class,
                "mikrotik_cards_database"
        )
        .fallbackToDestructiveMigration()
        .build();
    }

    /**
     * إعداد نظام التشفير
     */
    private void setupEncryption() {
        encryptionUtils = new EncryptionUtils(this);
    }

    /**
     * إعداد المجلدات المطلوبة
     */
    private void setupDirectories() {
        try {
            File appDir = new File(Environment.getExternalStorageDirectory(), APP_FOLDER);
            if (!appDir.exists()) {
                appDir.mkdirs();
            }

            // إنشاء المجلدات الفرعية
            String[] folders = {EXPORTS_FOLDER, BACKUPS_FOLDER, TEMPLATES_FOLDER, LOGS_FOLDER};
            for (String folder : folders) {
                File dir = new File(appDir, folder);
                if (!dir.exists()) {
                    dir.mkdirs();
                }
            }

            Timber.i("تم إنشاء مجلدات التطبيق بنجاح");
        } catch (Exception e) {
            Timber.e(e, "خطأ في إنشاء مجلدات التطبيق");
        }
    }

    /**
     * إعداد الإعدادات المشتركة
     */
    private void setupPreferences() {
        preferences = getSharedPreferences("mikrotik_settings", Context.MODE_PRIVATE);
    }

    /**
     * الحصول على قاعدة البيانات
     */
    public AppDatabase getDatabase() {
        return database;
    }

    /**
     * الحصول على الإعدادات المشتركة
     */
    public SharedPreferences getPreferences() {
        return preferences;
    }

    /**
     * الحصول على أدوات التشفير
     */
    public EncryptionUtils getEncryptionUtils() {
        return encryptionUtils;
    }

    /**
     * الحصول على مسار مجلد التطبيق
     */
    public File getAppDirectory() {
        return new File(Environment.getExternalStorageDirectory(), APP_FOLDER);
    }

    /**
     * الحصول على مسار مجلد فرعي
     */
    public File getSubDirectory(String folderName) {
        return new File(getAppDirectory(), folderName);
    }

    /**
     * حفظ إعداد في الإعدادات المشتركة
     */
    public void saveSetting(String key, String value) {
        preferences.edit().putString(key, value).apply();
    }

    /**
     * الحصول على إعداد من الإعدادات المشتركة
     */
    public String getSetting(String key, String defaultValue) {
        return preferences.getString(key, defaultValue);
    }

    /**
     * حفظ إعداد منطقي
     */
    public void saveBooleanSetting(String key, boolean value) {
        preferences.edit().putBoolean(key, value).apply();
    }

    /**
     * الحصول على إعداد منطقي
     */
    public boolean getBooleanSetting(String key, boolean defaultValue) {
        return preferences.getBoolean(key, defaultValue);
    }

    /**
     * حفظ إعداد رقمي
     */
    public void saveIntSetting(String key, int value) {
        preferences.edit().putInt(key, value).apply();
    }

    /**
     * الحصول على إعداد رقمي
     */
    public int getIntSetting(String key, int defaultValue) {
        return preferences.getInt(key, defaultValue);
    }
}
