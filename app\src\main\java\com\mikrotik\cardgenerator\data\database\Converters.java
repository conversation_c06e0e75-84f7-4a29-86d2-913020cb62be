package com.mikrotik.cardgenerator.data.database;

import androidx.room.TypeConverter;
import java.util.Date;

/**
 * محولات البيانات لقاعدة البيانات
 */
public class Converters {
    
    @TypeConverter
    public static Date fromTimestamp(Long value) {
        return value == null ? null : new Date(value);
    }
    
    @TypeConverter
    public static Long dateToTimestamp(Date date) {
        return date == null ? null : date.getTime();
    }
}
