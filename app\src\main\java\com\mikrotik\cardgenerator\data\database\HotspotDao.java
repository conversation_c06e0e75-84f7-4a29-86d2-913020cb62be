package com.mikrotik.cardgenerator.data.database;

import androidx.lifecycle.LiveData;
import androidx.room.*;
import com.mikrotik.cardgenerator.data.models.HotspotCredential;
import java.util.List;

/**
 * DAO لحسابات Hotspot
 */
@Dao
public interface HotspotDao {
    
    @Query("SELECT * FROM hotspot_credentials ORDER BY created_date DESC")
    LiveData<List<HotspotCredential>> getAllCredentials();
    
    @Query("SELECT * FROM hotspot_credentials WHERE is_active = 1 ORDER BY created_date DESC")
    LiveData<List<HotspotCredential>> getActiveCredentials();
    
    @Query("SELECT * FROM hotspot_credentials WHERE id = :id")
    LiveData<HotspotCredential> getCredentialById(int id);
    
    @Query("SELECT * FROM hotspot_credentials WHERE username = :username LIMIT 1")
    HotspotCredential getCredentialByUsername(String username);
    
    @Query("SELECT * FROM hotspot_credentials WHERE profile = :profile ORDER BY created_date DESC")
    LiveData<List<HotspotCredential>> getCredentialsByProfile(String profile);
    
    @Query("SELECT DISTINCT profile FROM hotspot_credentials WHERE profile IS NOT NULL AND profile != ''")
    LiveData<List<String>> getAllProfiles();
    
    @Query("SELECT COUNT(*) FROM hotspot_credentials")
    LiveData<Integer> getCredentialsCount();
    
    @Query("SELECT COUNT(*) FROM hotspot_credentials WHERE is_active = 1")
    LiveData<Integer> getActiveCredentialsCount();
    
    @Query("SELECT MAX(serial_number) FROM hotspot_credentials")
    int getLastSerialNumber();
    
    @Query("SELECT * FROM hotspot_credentials WHERE created_date BETWEEN :startDate AND :endDate ORDER BY created_date DESC")
    LiveData<List<HotspotCredential>> getCredentialsByDateRange(long startDate, long endDate);
    
    @Query("SELECT * FROM hotspot_credentials WHERE username LIKE :searchQuery OR comment LIKE :searchQuery OR location LIKE :searchQuery ORDER BY created_date DESC")
    LiveData<List<HotspotCredential>> searchCredentials(String searchQuery);
    
    @Query("SELECT * FROM hotspot_credentials WHERE limit_unit = :limitUnit ORDER BY created_date DESC")
    LiveData<List<HotspotCredential>> getCredentialsByLimitUnit(String limitUnit);
    
    @Insert
    long insertCredential(HotspotCredential credential);
    
    @Insert
    List<Long> insertCredentials(List<HotspotCredential> credentials);
    
    @Update
    void updateCredential(HotspotCredential credential);
    
    @Delete
    void deleteCredential(HotspotCredential credential);
    
    @Query("DELETE FROM hotspot_credentials WHERE id = :id")
    void deleteCredentialById(int id);
    
    @Query("DELETE FROM hotspot_credentials")
    void deleteAllCredentials();
    
    @Query("DELETE FROM hotspot_credentials WHERE is_active = 0")
    void deleteInactiveCredentials();
    
    @Query("UPDATE hotspot_credentials SET is_active = 0 WHERE id = :id")
    void deactivateCredential(int id);
    
    @Query("UPDATE hotspot_credentials SET is_active = 1 WHERE id = :id")
    void activateCredential(int id);
    
    @Query("UPDATE hotspot_credentials SET updated_date = :updateDate WHERE id = :id")
    void updateCredentialDate(int id, long updateDate);
}
