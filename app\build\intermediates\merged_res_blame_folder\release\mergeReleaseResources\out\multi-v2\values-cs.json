{"logs": [{"outputFile": "com.mikrotik.cardgenerator.app-mergeReleaseResources-57:/values-cs/values-cs.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\69e8a0e0d964fb1e9e45715fb440dd3e\\transformed\\material-1.12.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,368,446,524,601,704,796,888,1014,1095,1156,1221,1320,1396,1457,1546,1610,1677,1731,1799,1859,1913,2030,2090,2152,2206,2278,2400,2484,2563,2657,2740,2832,2969,3047,3129,3256,3344,3424,3478,3529,3595,3667,3744,3815,3896,3968,4045,4119,4190,4295,4383,4454,4547,4642,4716,4790,4886,4938,5021,5088,5174,5262,5324,5388,5451,5519,5629,5735,5834,5948,6006,6061,6140,6223,6298", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "endColumns": "12,77,77,76,102,91,91,125,80,60,64,98,75,60,88,63,66,53,67,59,53,116,59,61,53,71,121,83,78,93,82,91,136,77,81,126,87,79,53,50,65,71,76,70,80,71,76,73,70,104,87,70,92,94,73,73,95,51,82,66,85,87,61,63,62,67,109,105,98,113,57,54,78,82,74,78", "endOffsets": "363,441,519,596,699,791,883,1009,1090,1151,1216,1315,1391,1452,1541,1605,1672,1726,1794,1854,1908,2025,2085,2147,2201,2273,2395,2479,2558,2652,2735,2827,2964,3042,3124,3251,3339,3419,3473,3524,3590,3662,3739,3810,3891,3963,4040,4114,4185,4290,4378,4449,4542,4637,4711,4785,4881,4933,5016,5083,5169,5257,5319,5383,5446,5514,5624,5730,5829,5943,6001,6056,6135,6218,6293,6372"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,115,117,118,119", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3112,3190,3268,3345,3448,4271,4363,4489,4570,4631,4696,4795,4871,4932,5021,5085,5152,5206,5274,5334,5388,5505,5565,5627,5681,5753,5875,5959,6038,6132,6215,6307,6444,6522,6604,6731,6819,6899,6953,7004,7070,7142,7219,7290,7371,7443,7520,7594,7665,7770,7858,7929,8022,8117,8191,8265,8361,8413,8496,8563,8649,8737,8799,8863,8926,8994,9104,9210,9309,9423,9481,9771,9933,10016,10091", "endLines": "7,35,36,37,38,39,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,115,117,118,119", "endColumns": "12,77,77,76,102,91,91,125,80,60,64,98,75,60,88,63,66,53,67,59,53,116,59,61,53,71,121,83,78,93,82,91,136,77,81,126,87,79,53,50,65,71,76,70,80,71,76,73,70,104,87,70,92,94,73,73,95,51,82,66,85,87,61,63,62,67,109,105,98,113,57,54,78,82,74,78", "endOffsets": "413,3185,3263,3340,3443,3535,4358,4484,4565,4626,4691,4790,4866,4927,5016,5080,5147,5201,5269,5329,5383,5500,5560,5622,5676,5748,5870,5954,6033,6127,6210,6302,6439,6517,6599,6726,6814,6894,6948,6999,7065,7137,7214,7285,7366,7438,7515,7589,7660,7765,7853,7924,8017,8112,8186,8260,8356,8408,8491,8558,8644,8732,8794,8858,8921,8989,9099,9205,9304,9418,9476,9531,9845,10011,10086,10165"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fb7148cc8318b09aaa0d36d8e3e2b12c\\transformed\\navigation-ui-2.7.6\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,163", "endColumns": "107,126", "endOffsets": "158,285"}, "to": {"startLines": "113,114", "startColumns": "4,4", "startOffsets": "9536,9644", "endColumns": "107,126", "endOffsets": "9639,9766"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1772ff301a09aacb61759aedab678ec4\\transformed\\zxing-android-embedded-4.3.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,114,161,311", "endColumns": "58,46,149,83", "endOffsets": "109,156,306,390"}, "to": {"startLines": "121,122,123,124", "startColumns": "4,4,4,4", "startOffsets": "10271,10330,10377,10527", "endColumns": "58,46,149,83", "endOffsets": "10325,10372,10522,10606"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\412d4fb45e796eeac261181f9a418e93\\transformed\\appcompat-1.7.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,424,510,615,732,810,886,977,1070,1165,1259,1353,1446,1541,1638,1729,1820,1904,2008,2120,2219,2325,2436,2538,2701,2799", "endColumns": "106,101,109,85,104,116,77,75,90,92,94,93,93,92,94,96,90,90,83,103,111,98,105,110,101,162,97,82", "endOffsets": "207,309,419,505,610,727,805,881,972,1065,1160,1254,1348,1441,1536,1633,1724,1815,1899,2003,2115,2214,2320,2431,2533,2696,2794,2877"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "418,525,627,737,823,928,1045,1123,1199,1290,1383,1478,1572,1666,1759,1854,1951,2042,2133,2217,2321,2433,2532,2638,2749,2851,3014,9850", "endColumns": "106,101,109,85,104,116,77,75,90,92,94,93,93,92,94,96,90,90,83,103,111,98,105,110,101,162,97,82", "endOffsets": "520,622,732,818,923,1040,1118,1194,1285,1378,1473,1567,1661,1754,1849,1946,2037,2128,2212,2316,2428,2527,2633,2744,2846,3009,3107,9928"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\476525ada1d35df1ee329f5bd94fbe69\\transformed\\core-1.13.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,455,560,667,786", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "148,250,351,450,555,662,781,882"}, "to": {"startLines": "40,41,42,43,44,45,46,120", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3540,3638,3740,3841,3940,4045,4152,10170", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "3633,3735,3836,3935,4040,4147,4266,10266"}}]}]}