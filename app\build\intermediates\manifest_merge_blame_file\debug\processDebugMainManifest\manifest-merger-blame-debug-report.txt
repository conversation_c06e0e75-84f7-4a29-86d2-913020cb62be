1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.mikrotik.cardgenerator"
4    android:versionCode="1"
5    android:versionName="2.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="35" />
10
11    <!-- الأذونات المطلوبة -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:7:5-79
13-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
14-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:8:5-76
14-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:8:22-73
15    <uses-permission
15-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:9:5-10:38
16        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
16-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:9:22-78
17        android:maxSdkVersion="28" />
17-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:10:9-35
18    <uses-permission
18-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:11:5-12:38
19        android:name="android.permission.READ_EXTERNAL_STORAGE"
19-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:11:22-77
20        android:maxSdkVersion="32" />
20-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:12:9-35
21    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
21-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:13:5-76
21-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:13:22-73
22    <uses-permission android:name="android.permission.CAMERA" />
22-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:14:5-65
22-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:14:22-62
23    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
23-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:15:5-16:40
23-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:15:22-79
24
25    <permission
25-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476525ada1d35df1ee329f5bd94fbe69\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
26        android:name="com.mikrotik.cardgenerator.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
26-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476525ada1d35df1ee329f5bd94fbe69\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
27        android:protectionLevel="signature" />
27-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476525ada1d35df1ee329f5bd94fbe69\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
28
29    <uses-permission android:name="com.mikrotik.cardgenerator.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" /> <!-- Don't require camera, as this requires a rear camera. This allows it to work on the Nexus 7 -->
29-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476525ada1d35df1ee329f5bd94fbe69\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
29-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476525ada1d35df1ee329f5bd94fbe69\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
30    <uses-feature
30-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1772ff301a09aacb61759aedab678ec4\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:25:5-27:36
31        android:name="android.hardware.camera"
31-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1772ff301a09aacb61759aedab678ec4\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:26:9-47
32        android:required="false" />
32-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1772ff301a09aacb61759aedab678ec4\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:27:9-33
33    <uses-feature
33-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1772ff301a09aacb61759aedab678ec4\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:28:5-30:36
34        android:name="android.hardware.camera.front"
34-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1772ff301a09aacb61759aedab678ec4\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:29:9-53
35        android:required="false" /> <!-- TODO replace above two with next line after Android 4.2 -->
35-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1772ff301a09aacb61759aedab678ec4\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:30:9-33
36    <!-- <uses-feature android:name="android.hardware.camera.any"/> -->
37    <uses-feature
37-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1772ff301a09aacb61759aedab678ec4\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:33:5-35:36
38        android:name="android.hardware.camera.autofocus"
38-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1772ff301a09aacb61759aedab678ec4\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:34:9-57
39        android:required="false" />
39-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1772ff301a09aacb61759aedab678ec4\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:35:9-33
40    <uses-feature
40-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1772ff301a09aacb61759aedab678ec4\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:36:5-38:36
41        android:name="android.hardware.camera.flash"
41-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1772ff301a09aacb61759aedab678ec4\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:37:9-53
42        android:required="false" />
42-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1772ff301a09aacb61759aedab678ec4\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:38:9-33
43    <uses-feature
43-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1772ff301a09aacb61759aedab678ec4\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:39:5-41:36
44        android:name="android.hardware.screen.landscape"
44-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1772ff301a09aacb61759aedab678ec4\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:40:9-57
45        android:required="false" />
45-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1772ff301a09aacb61759aedab678ec4\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:41:9-33
46    <uses-feature
46-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1772ff301a09aacb61759aedab678ec4\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:42:5-44:36
47        android:name="android.hardware.wifi"
47-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1772ff301a09aacb61759aedab678ec4\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:43:9-45
48        android:required="false" />
48-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1772ff301a09aacb61759aedab678ec4\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:44:9-33
49
50    <application
50-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:18:5-80:19
51        android:name="com.mikrotik.cardgenerator.MikroTikApplication"
51-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:19:9-44
52        android:allowBackup="true"
52-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:20:9-35
53        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
53-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476525ada1d35df1ee329f5bd94fbe69\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
54        android:dataExtractionRules="@xml/data_extraction_rules"
54-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:21:9-65
55        android:debuggable="true"
56        android:extractNativeLibs="false"
57        android:fullBackupContent="@xml/backup_rules"
57-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:22:9-54
58        android:icon="@mipmap/ic_launcher"
58-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:23:9-43
59        android:label="@string/app_name"
59-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:24:9-41
60        android:preserveLegacyExternalStorage="true"
60-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:29:9-53
61        android:requestLegacyExternalStorage="true"
61-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:28:9-52
62        android:roundIcon="@mipmap/ic_launcher_round"
62-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:25:9-54
63        android:supportsRtl="true"
63-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:26:9-35
64        android:testOnly="true"
65        android:theme="@style/Theme.MikroTikCardGenerator" >
65-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:27:9-59
66
67        <!-- الشاشة الرئيسية -->
68        <activity
68-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:33:9-42:20
69            android:name="com.mikrotik.cardgenerator.ui.MainActivity"
69-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:34:13-44
70            android:exported="true"
70-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:35:13-36
71            android:screenOrientation="portrait"
71-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:36:13-49
72            android:windowSoftInputMode="adjustResize" >
72-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:37:13-55
73            <intent-filter>
73-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:38:13-41:29
74                <action android:name="android.intent.action.MAIN" />
74-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:39:17-69
74-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:39:25-66
75
76                <category android:name="android.intent.category.LAUNCHER" />
76-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:40:17-77
76-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:40:27-74
77            </intent-filter>
78        </activity>
79
80        <!-- شاشة اختيار النظام -->
81        <activity
81-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:45:9-48:52
82            android:name="com.mikrotik.cardgenerator.ui.SystemSelectionActivity"
82-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:46:13-55
83            android:exported="false"
83-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:47:13-37
84            android:screenOrientation="portrait" />
84-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:48:13-49
85
86        <!-- شاشة توليد الكروت -->
87        <activity
87-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:51:9-55:58
88            android:name="com.mikrotik.cardgenerator.ui.CardGeneratorActivity"
88-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:52:13-53
89            android:exported="false"
89-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:53:13-37
90            android:screenOrientation="portrait"
90-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:54:13-49
91            android:windowSoftInputMode="adjustResize" />
91-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:55:13-55
92
93        <!-- شاشة إعدادات الاتصال -->
94        <activity
94-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:58:9-61:52
95            android:name="com.mikrotik.cardgenerator.ui.ConnectionSettingsActivity"
95-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:59:13-58
96            android:exported="false"
96-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:60:13-37
97            android:screenOrientation="portrait" />
97-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:61:13-49
98
99        <!-- شاشة معاينة PDF -->
100        <activity
100-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:64:9-67:52
101            android:name="com.mikrotik.cardgenerator.ui.PdfPreviewActivity"
101-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:65:13-50
102            android:exported="false"
102-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:66:13-37
103            android:screenOrientation="portrait" />
103-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:67:13-49
104
105        <!-- File Provider للوصول للملفات -->
106        <provider
107            android:name="androidx.core.content.FileProvider"
107-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:71:13-62
108            android:authorities="com.mikrotik.cardgenerator.fileprovider"
108-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:72:13-64
109            android:exported="false"
109-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:73:13-37
110            android:grantUriPermissions="true" >
110-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:74:13-47
111            <meta-data
111-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:75:13-77:54
112                android:name="android.support.FILE_PROVIDER_PATHS"
112-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:76:17-67
113                android:resource="@xml/file_paths" />
113-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:77:17-51
114        </provider>
115
116        <activity
116-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69e6f47866a4eaec992fd3042f3973fa\transformed\dexter-6.2.3\AndroidManifest.xml:27:9-29:72
117            android:name="com.karumi.dexter.DexterActivity"
117-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69e6f47866a4eaec992fd3042f3973fa\transformed\dexter-6.2.3\AndroidManifest.xml:28:13-60
118            android:theme="@style/Dexter.Internal.Theme.Transparent" />
118-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69e6f47866a4eaec992fd3042f3973fa\transformed\dexter-6.2.3\AndroidManifest.xml:29:13-69
119
120        <provider
120-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c97984e08a3d1ad94cf20e956055eb58\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
121            android:name="androidx.startup.InitializationProvider"
121-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c97984e08a3d1ad94cf20e956055eb58\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
122            android:authorities="com.mikrotik.cardgenerator.androidx-startup"
122-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c97984e08a3d1ad94cf20e956055eb58\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
123            android:exported="false" >
123-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c97984e08a3d1ad94cf20e956055eb58\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
124            <meta-data
124-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c97984e08a3d1ad94cf20e956055eb58\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
125                android:name="androidx.emoji2.text.EmojiCompatInitializer"
125-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c97984e08a3d1ad94cf20e956055eb58\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
126                android:value="androidx.startup" />
126-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c97984e08a3d1ad94cf20e956055eb58\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
127            <meta-data
127-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bad75f318ec05692b92dc56f1a7f9d6b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
128                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
128-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bad75f318ec05692b92dc56f1a7f9d6b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
129                android:value="androidx.startup" />
129-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bad75f318ec05692b92dc56f1a7f9d6b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
130            <meta-data
130-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
131                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
131-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
132                android:value="androidx.startup" />
132-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
133        </provider>
134
135        <uses-library
135-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\13a59fa95409d2015886ebe40fb0f314\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
136            android:name="androidx.window.extensions"
136-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\13a59fa95409d2015886ebe40fb0f314\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
137            android:required="false" />
137-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\13a59fa95409d2015886ebe40fb0f314\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
138        <uses-library
138-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\13a59fa95409d2015886ebe40fb0f314\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
139            android:name="androidx.window.sidecar"
139-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\13a59fa95409d2015886ebe40fb0f314\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
140            android:required="false" />
140-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\13a59fa95409d2015886ebe40fb0f314\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
141
142        <service
142-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e82a49340ca0c527f750264087cd9c54\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
143            android:name="androidx.room.MultiInstanceInvalidationService"
143-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e82a49340ca0c527f750264087cd9c54\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
144            android:directBootAware="true"
144-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e82a49340ca0c527f750264087cd9c54\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
145            android:exported="false" />
145-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e82a49340ca0c527f750264087cd9c54\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
146
147        <activity
147-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1772ff301a09aacb61759aedab678ec4\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:47:9-53:63
148            android:name="com.journeyapps.barcodescanner.CaptureActivity"
148-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1772ff301a09aacb61759aedab678ec4\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:48:13-74
149            android:clearTaskOnLaunch="true"
149-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1772ff301a09aacb61759aedab678ec4\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:49:13-45
150            android:screenOrientation="sensorLandscape"
150-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1772ff301a09aacb61759aedab678ec4\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:50:13-56
151            android:stateNotNeeded="true"
151-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1772ff301a09aacb61759aedab678ec4\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:51:13-42
152            android:theme="@style/zxing_CaptureTheme"
152-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1772ff301a09aacb61759aedab678ec4\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:52:13-54
153            android:windowSoftInputMode="stateAlwaysHidden" />
153-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1772ff301a09aacb61759aedab678ec4\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:53:13-60
154
155        <receiver
155-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
156            android:name="androidx.profileinstaller.ProfileInstallReceiver"
156-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
157            android:directBootAware="false"
157-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
158            android:enabled="true"
158-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
159            android:exported="true"
159-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
160            android:permission="android.permission.DUMP" >
160-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
161            <intent-filter>
161-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
162                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
162-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
162-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
163            </intent-filter>
164            <intent-filter>
164-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
165                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
165-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
165-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
166            </intent-filter>
167            <intent-filter>
167-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
168                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
168-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
168-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
169            </intent-filter>
170            <intent-filter>
170-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
171                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
171-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
171-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
172            </intent-filter>
173        </receiver>
174    </application>
175
176</manifest>
