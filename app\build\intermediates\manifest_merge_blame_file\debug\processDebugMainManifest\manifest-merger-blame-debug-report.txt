1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.mikrotik.cardgenerator"
4    android:versionCode="1"
5    android:versionName="2.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="35" />
10
11    <!-- الأذونات المطلوبة -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:7:5-79
13-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
14-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:8:5-76
14-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:8:22-73
15    <uses-permission
15-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:9:5-10:38
16        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
16-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:9:22-78
17        android:maxSdkVersion="28" />
17-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:10:9-35
18    <uses-permission
18-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:11:5-12:38
19        android:name="android.permission.READ_EXTERNAL_STORAGE"
19-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:11:22-77
20        android:maxSdkVersion="32" />
20-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:12:9-35
21    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
21-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:13:5-76
21-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:13:22-73
22    <uses-permission android:name="android.permission.CAMERA" />
22-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:14:5-65
22-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:14:22-62
23    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
23-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:15:5-16:40
23-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:15:22-79
24
25    <permission
25-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476525ada1d35df1ee329f5bd94fbe69\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
26        android:name="com.mikrotik.cardgenerator.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
26-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476525ada1d35df1ee329f5bd94fbe69\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
27        android:protectionLevel="signature" />
27-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476525ada1d35df1ee329f5bd94fbe69\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
28
29    <uses-permission android:name="com.mikrotik.cardgenerator.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" /> <!-- Don't require camera, as this requires a rear camera. This allows it to work on the Nexus 7 -->
29-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476525ada1d35df1ee329f5bd94fbe69\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
29-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476525ada1d35df1ee329f5bd94fbe69\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
30    <uses-feature
30-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1772ff301a09aacb61759aedab678ec4\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:25:5-27:36
31        android:name="android.hardware.camera"
31-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1772ff301a09aacb61759aedab678ec4\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:26:9-47
32        android:required="false" />
32-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1772ff301a09aacb61759aedab678ec4\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:27:9-33
33    <uses-feature
33-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1772ff301a09aacb61759aedab678ec4\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:28:5-30:36
34        android:name="android.hardware.camera.front"
34-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1772ff301a09aacb61759aedab678ec4\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:29:9-53
35        android:required="false" /> <!-- TODO replace above two with next line after Android 4.2 -->
35-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1772ff301a09aacb61759aedab678ec4\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:30:9-33
36    <!-- <uses-feature android:name="android.hardware.camera.any"/> -->
37    <uses-feature
37-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1772ff301a09aacb61759aedab678ec4\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:33:5-35:36
38        android:name="android.hardware.camera.autofocus"
38-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1772ff301a09aacb61759aedab678ec4\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:34:9-57
39        android:required="false" />
39-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1772ff301a09aacb61759aedab678ec4\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:35:9-33
40    <uses-feature
40-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1772ff301a09aacb61759aedab678ec4\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:36:5-38:36
41        android:name="android.hardware.camera.flash"
41-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1772ff301a09aacb61759aedab678ec4\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:37:9-53
42        android:required="false" />
42-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1772ff301a09aacb61759aedab678ec4\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:38:9-33
43    <uses-feature
43-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1772ff301a09aacb61759aedab678ec4\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:39:5-41:36
44        android:name="android.hardware.screen.landscape"
44-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1772ff301a09aacb61759aedab678ec4\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:40:9-57
45        android:required="false" />
45-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1772ff301a09aacb61759aedab678ec4\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:41:9-33
46    <uses-feature
46-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1772ff301a09aacb61759aedab678ec4\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:42:5-44:36
47        android:name="android.hardware.wifi"
47-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1772ff301a09aacb61759aedab678ec4\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:43:9-45
48        android:required="false" />
48-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1772ff301a09aacb61759aedab678ec4\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:44:9-33
49
50    <application
50-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:18:5-80:19
51        android:name="com.mikrotik.cardgenerator.MikroTikApplication"
51-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:19:9-44
52        android:allowBackup="true"
52-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:20:9-35
53        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
53-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476525ada1d35df1ee329f5bd94fbe69\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
54        android:dataExtractionRules="@xml/data_extraction_rules"
54-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:21:9-65
55        android:debuggable="true"
56        android:extractNativeLibs="false"
57        android:fullBackupContent="@xml/backup_rules"
57-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:22:9-54
58        android:icon="@mipmap/ic_launcher"
58-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:23:9-43
59        android:label="@string/app_name"
59-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:24:9-41
60        android:preserveLegacyExternalStorage="true"
60-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:29:9-53
61        android:requestLegacyExternalStorage="true"
61-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:28:9-52
62        android:roundIcon="@mipmap/ic_launcher_round"
62-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:25:9-54
63        android:supportsRtl="true"
63-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:26:9-35
64        android:theme="@style/Theme.MikroTikCardGenerator" >
64-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:27:9-59
65
66        <!-- الشاشة الرئيسية -->
67        <activity
67-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:33:9-42:20
68            android:name="com.mikrotik.cardgenerator.ui.MainActivity"
68-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:34:13-44
69            android:exported="true"
69-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:35:13-36
70            android:screenOrientation="portrait"
70-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:36:13-49
71            android:windowSoftInputMode="adjustResize" >
71-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:37:13-55
72            <intent-filter>
72-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:38:13-41:29
73                <action android:name="android.intent.action.MAIN" />
73-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:39:17-69
73-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:39:25-66
74
75                <category android:name="android.intent.category.LAUNCHER" />
75-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:40:17-77
75-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:40:27-74
76            </intent-filter>
77        </activity>
78
79        <!-- شاشة اختيار النظام -->
80        <activity
80-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:45:9-48:52
81            android:name="com.mikrotik.cardgenerator.ui.SystemSelectionActivity"
81-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:46:13-55
82            android:exported="false"
82-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:47:13-37
83            android:screenOrientation="portrait" />
83-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:48:13-49
84
85        <!-- شاشة توليد الكروت -->
86        <activity
86-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:51:9-55:58
87            android:name="com.mikrotik.cardgenerator.ui.CardGeneratorActivity"
87-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:52:13-53
88            android:exported="false"
88-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:53:13-37
89            android:screenOrientation="portrait"
89-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:54:13-49
90            android:windowSoftInputMode="adjustResize" />
90-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:55:13-55
91
92        <!-- شاشة إعدادات الاتصال -->
93        <activity
93-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:58:9-61:52
94            android:name="com.mikrotik.cardgenerator.ui.ConnectionSettingsActivity"
94-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:59:13-58
95            android:exported="false"
95-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:60:13-37
96            android:screenOrientation="portrait" />
96-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:61:13-49
97
98        <!-- شاشة معاينة PDF -->
99        <activity
99-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:64:9-67:52
100            android:name="com.mikrotik.cardgenerator.ui.PdfPreviewActivity"
100-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:65:13-50
101            android:exported="false"
101-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:66:13-37
102            android:screenOrientation="portrait" />
102-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:67:13-49
103
104        <!-- File Provider للوصول للملفات -->
105        <provider
106            android:name="androidx.core.content.FileProvider"
106-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:71:13-62
107            android:authorities="com.mikrotik.cardgenerator.fileprovider"
107-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:72:13-64
108            android:exported="false"
108-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:73:13-37
109            android:grantUriPermissions="true" >
109-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:74:13-47
110            <meta-data
110-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:75:13-77:54
111                android:name="android.support.FILE_PROVIDER_PATHS"
111-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:76:17-67
112                android:resource="@xml/file_paths" />
112-->C:\Users\<USER>\Desktop\New folder (4)\app\src\main\AndroidManifest.xml:77:17-51
113        </provider>
114
115        <activity
115-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69e6f47866a4eaec992fd3042f3973fa\transformed\dexter-6.2.3\AndroidManifest.xml:27:9-29:72
116            android:name="com.karumi.dexter.DexterActivity"
116-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69e6f47866a4eaec992fd3042f3973fa\transformed\dexter-6.2.3\AndroidManifest.xml:28:13-60
117            android:theme="@style/Dexter.Internal.Theme.Transparent" />
117-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69e6f47866a4eaec992fd3042f3973fa\transformed\dexter-6.2.3\AndroidManifest.xml:29:13-69
118
119        <provider
119-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c97984e08a3d1ad94cf20e956055eb58\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
120            android:name="androidx.startup.InitializationProvider"
120-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c97984e08a3d1ad94cf20e956055eb58\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
121            android:authorities="com.mikrotik.cardgenerator.androidx-startup"
121-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c97984e08a3d1ad94cf20e956055eb58\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
122            android:exported="false" >
122-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c97984e08a3d1ad94cf20e956055eb58\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
123            <meta-data
123-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c97984e08a3d1ad94cf20e956055eb58\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
124                android:name="androidx.emoji2.text.EmojiCompatInitializer"
124-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c97984e08a3d1ad94cf20e956055eb58\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
125                android:value="androidx.startup" />
125-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c97984e08a3d1ad94cf20e956055eb58\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
126            <meta-data
126-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bad75f318ec05692b92dc56f1a7f9d6b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
127                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
127-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bad75f318ec05692b92dc56f1a7f9d6b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
128                android:value="androidx.startup" />
128-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bad75f318ec05692b92dc56f1a7f9d6b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
129            <meta-data
129-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
130                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
130-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
131                android:value="androidx.startup" />
131-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
132        </provider>
133
134        <uses-library
134-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\13a59fa95409d2015886ebe40fb0f314\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
135            android:name="androidx.window.extensions"
135-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\13a59fa95409d2015886ebe40fb0f314\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
136            android:required="false" />
136-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\13a59fa95409d2015886ebe40fb0f314\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
137        <uses-library
137-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\13a59fa95409d2015886ebe40fb0f314\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
138            android:name="androidx.window.sidecar"
138-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\13a59fa95409d2015886ebe40fb0f314\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
139            android:required="false" />
139-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\13a59fa95409d2015886ebe40fb0f314\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
140
141        <service
141-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e82a49340ca0c527f750264087cd9c54\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
142            android:name="androidx.room.MultiInstanceInvalidationService"
142-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e82a49340ca0c527f750264087cd9c54\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
143            android:directBootAware="true"
143-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e82a49340ca0c527f750264087cd9c54\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
144            android:exported="false" />
144-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e82a49340ca0c527f750264087cd9c54\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
145
146        <activity
146-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1772ff301a09aacb61759aedab678ec4\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:47:9-53:63
147            android:name="com.journeyapps.barcodescanner.CaptureActivity"
147-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1772ff301a09aacb61759aedab678ec4\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:48:13-74
148            android:clearTaskOnLaunch="true"
148-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1772ff301a09aacb61759aedab678ec4\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:49:13-45
149            android:screenOrientation="sensorLandscape"
149-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1772ff301a09aacb61759aedab678ec4\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:50:13-56
150            android:stateNotNeeded="true"
150-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1772ff301a09aacb61759aedab678ec4\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:51:13-42
151            android:theme="@style/zxing_CaptureTheme"
151-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1772ff301a09aacb61759aedab678ec4\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:52:13-54
152            android:windowSoftInputMode="stateAlwaysHidden" />
152-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1772ff301a09aacb61759aedab678ec4\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:53:13-60
153
154        <receiver
154-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
155            android:name="androidx.profileinstaller.ProfileInstallReceiver"
155-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
156            android:directBootAware="false"
156-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
157            android:enabled="true"
157-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
158            android:exported="true"
158-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
159            android:permission="android.permission.DUMP" >
159-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
160            <intent-filter>
160-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
161                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
161-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
161-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
162            </intent-filter>
163            <intent-filter>
163-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
164                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
164-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
164-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
165            </intent-filter>
166            <intent-filter>
166-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
167                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
167-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
167-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
168            </intent-filter>
169            <intent-filter>
169-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
170                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
170-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
170-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
171            </intent-filter>
172        </receiver>
173    </application>
174
175</manifest>
