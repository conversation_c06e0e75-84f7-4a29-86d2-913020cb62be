package com.mikrotik.cardgenerator.data.database;

import android.database.Cursor;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.LiveData;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.mikrotik.cardgenerator.data.models.HotspotCredential;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class HotspotDao_Impl implements HotspotDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<HotspotCredential> __insertionAdapterOfHotspotCredential;

  private final EntityDeletionOrUpdateAdapter<HotspotCredential> __deletionAdapterOfHotspotCredential;

  private final EntityDeletionOrUpdateAdapter<HotspotCredential> __updateAdapterOfHotspotCredential;

  private final SharedSQLiteStatement __preparedStmtOfDeleteCredentialById;

  private final SharedSQLiteStatement __preparedStmtOfDeleteAllCredentials;

  private final SharedSQLiteStatement __preparedStmtOfDeleteInactiveCredentials;

  private final SharedSQLiteStatement __preparedStmtOfDeactivateCredential;

  private final SharedSQLiteStatement __preparedStmtOfActivateCredential;

  private final SharedSQLiteStatement __preparedStmtOfUpdateCredentialDate;

  public HotspotDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfHotspotCredential = new EntityInsertionAdapter<HotspotCredential>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR ABORT INTO `hotspot_credentials` (`id`,`username`,`password`,`profile`,`comment`,`location`,`limit_bytes`,`limit_unit`,`days`,`email_template`,`price`,`created_date`,`updated_date`,`is_active`,`serial_number`,`uptime_limit`,`rate_limit`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          final HotspotCredential entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getUsername() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getUsername());
        }
        if (entity.getPassword() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getPassword());
        }
        if (entity.getProfile() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getProfile());
        }
        if (entity.getComment() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getComment());
        }
        if (entity.getLocation() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getLocation());
        }
        if (entity.getLimitBytes() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getLimitBytes());
        }
        if (entity.getLimitUnit() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getLimitUnit());
        }
        if (entity.getDays() == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, entity.getDays());
        }
        if (entity.getEmailTemplate() == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, entity.getEmailTemplate());
        }
        if (entity.getPrice() == null) {
          statement.bindNull(11);
        } else {
          statement.bindString(11, entity.getPrice());
        }
        final Long _tmp = Converters.dateToTimestamp(entity.getCreatedDate());
        if (_tmp == null) {
          statement.bindNull(12);
        } else {
          statement.bindLong(12, _tmp);
        }
        final Long _tmp_1 = Converters.dateToTimestamp(entity.getUpdatedDate());
        if (_tmp_1 == null) {
          statement.bindNull(13);
        } else {
          statement.bindLong(13, _tmp_1);
        }
        final int _tmp_2 = entity.isActive() ? 1 : 0;
        statement.bindLong(14, _tmp_2);
        statement.bindLong(15, entity.getSerialNumber());
        if (entity.getUptimeLimit() == null) {
          statement.bindNull(16);
        } else {
          statement.bindString(16, entity.getUptimeLimit());
        }
        if (entity.getRateLimit() == null) {
          statement.bindNull(17);
        } else {
          statement.bindString(17, entity.getRateLimit());
        }
      }
    };
    this.__deletionAdapterOfHotspotCredential = new EntityDeletionOrUpdateAdapter<HotspotCredential>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `hotspot_credentials` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          final HotspotCredential entity) {
        statement.bindLong(1, entity.getId());
      }
    };
    this.__updateAdapterOfHotspotCredential = new EntityDeletionOrUpdateAdapter<HotspotCredential>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `hotspot_credentials` SET `id` = ?,`username` = ?,`password` = ?,`profile` = ?,`comment` = ?,`location` = ?,`limit_bytes` = ?,`limit_unit` = ?,`days` = ?,`email_template` = ?,`price` = ?,`created_date` = ?,`updated_date` = ?,`is_active` = ?,`serial_number` = ?,`uptime_limit` = ?,`rate_limit` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          final HotspotCredential entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getUsername() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getUsername());
        }
        if (entity.getPassword() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getPassword());
        }
        if (entity.getProfile() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getProfile());
        }
        if (entity.getComment() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getComment());
        }
        if (entity.getLocation() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getLocation());
        }
        if (entity.getLimitBytes() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getLimitBytes());
        }
        if (entity.getLimitUnit() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getLimitUnit());
        }
        if (entity.getDays() == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, entity.getDays());
        }
        if (entity.getEmailTemplate() == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, entity.getEmailTemplate());
        }
        if (entity.getPrice() == null) {
          statement.bindNull(11);
        } else {
          statement.bindString(11, entity.getPrice());
        }
        final Long _tmp = Converters.dateToTimestamp(entity.getCreatedDate());
        if (_tmp == null) {
          statement.bindNull(12);
        } else {
          statement.bindLong(12, _tmp);
        }
        final Long _tmp_1 = Converters.dateToTimestamp(entity.getUpdatedDate());
        if (_tmp_1 == null) {
          statement.bindNull(13);
        } else {
          statement.bindLong(13, _tmp_1);
        }
        final int _tmp_2 = entity.isActive() ? 1 : 0;
        statement.bindLong(14, _tmp_2);
        statement.bindLong(15, entity.getSerialNumber());
        if (entity.getUptimeLimit() == null) {
          statement.bindNull(16);
        } else {
          statement.bindString(16, entity.getUptimeLimit());
        }
        if (entity.getRateLimit() == null) {
          statement.bindNull(17);
        } else {
          statement.bindString(17, entity.getRateLimit());
        }
        statement.bindLong(18, entity.getId());
      }
    };
    this.__preparedStmtOfDeleteCredentialById = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM hotspot_credentials WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteAllCredentials = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM hotspot_credentials";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteInactiveCredentials = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM hotspot_credentials WHERE is_active = 0";
        return _query;
      }
    };
    this.__preparedStmtOfDeactivateCredential = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE hotspot_credentials SET is_active = 0 WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfActivateCredential = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE hotspot_credentials SET is_active = 1 WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateCredentialDate = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE hotspot_credentials SET updated_date = ? WHERE id = ?";
        return _query;
      }
    };
  }

  @Override
  public long insertCredential(final HotspotCredential credential) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      final long _result = __insertionAdapterOfHotspotCredential.insertAndReturnId(credential);
      __db.setTransactionSuccessful();
      return _result;
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public List<Long> insertCredentials(final List<HotspotCredential> credentials) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      final List<Long> _result = __insertionAdapterOfHotspotCredential.insertAndReturnIdsList(credentials);
      __db.setTransactionSuccessful();
      return _result;
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public void deleteCredential(final HotspotCredential credential) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      __deletionAdapterOfHotspotCredential.handle(credential);
      __db.setTransactionSuccessful();
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public void updateCredential(final HotspotCredential credential) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      __updateAdapterOfHotspotCredential.handle(credential);
      __db.setTransactionSuccessful();
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public void deleteCredentialById(final int id) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteCredentialById.acquire();
    int _argIndex = 1;
    _stmt.bindLong(_argIndex, id);
    try {
      __db.beginTransaction();
      try {
        _stmt.executeUpdateDelete();
        __db.setTransactionSuccessful();
      } finally {
        __db.endTransaction();
      }
    } finally {
      __preparedStmtOfDeleteCredentialById.release(_stmt);
    }
  }

  @Override
  public void deleteAllCredentials() {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAllCredentials.acquire();
    try {
      __db.beginTransaction();
      try {
        _stmt.executeUpdateDelete();
        __db.setTransactionSuccessful();
      } finally {
        __db.endTransaction();
      }
    } finally {
      __preparedStmtOfDeleteAllCredentials.release(_stmt);
    }
  }

  @Override
  public void deleteInactiveCredentials() {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteInactiveCredentials.acquire();
    try {
      __db.beginTransaction();
      try {
        _stmt.executeUpdateDelete();
        __db.setTransactionSuccessful();
      } finally {
        __db.endTransaction();
      }
    } finally {
      __preparedStmtOfDeleteInactiveCredentials.release(_stmt);
    }
  }

  @Override
  public void deactivateCredential(final int id) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfDeactivateCredential.acquire();
    int _argIndex = 1;
    _stmt.bindLong(_argIndex, id);
    try {
      __db.beginTransaction();
      try {
        _stmt.executeUpdateDelete();
        __db.setTransactionSuccessful();
      } finally {
        __db.endTransaction();
      }
    } finally {
      __preparedStmtOfDeactivateCredential.release(_stmt);
    }
  }

  @Override
  public void activateCredential(final int id) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfActivateCredential.acquire();
    int _argIndex = 1;
    _stmt.bindLong(_argIndex, id);
    try {
      __db.beginTransaction();
      try {
        _stmt.executeUpdateDelete();
        __db.setTransactionSuccessful();
      } finally {
        __db.endTransaction();
      }
    } finally {
      __preparedStmtOfActivateCredential.release(_stmt);
    }
  }

  @Override
  public void updateCredentialDate(final int id, final long updateDate) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateCredentialDate.acquire();
    int _argIndex = 1;
    _stmt.bindLong(_argIndex, updateDate);
    _argIndex = 2;
    _stmt.bindLong(_argIndex, id);
    try {
      __db.beginTransaction();
      try {
        _stmt.executeUpdateDelete();
        __db.setTransactionSuccessful();
      } finally {
        __db.endTransaction();
      }
    } finally {
      __preparedStmtOfUpdateCredentialDate.release(_stmt);
    }
  }

  @Override
  public LiveData<List<HotspotCredential>> getAllCredentials() {
    final String _sql = "SELECT * FROM hotspot_credentials ORDER BY created_date DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return __db.getInvalidationTracker().createLiveData(new String[] {"hotspot_credentials"}, false, new Callable<List<HotspotCredential>>() {
      @Override
      @Nullable
      public List<HotspotCredential> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUsername = CursorUtil.getColumnIndexOrThrow(_cursor, "username");
          final int _cursorIndexOfPassword = CursorUtil.getColumnIndexOrThrow(_cursor, "password");
          final int _cursorIndexOfProfile = CursorUtil.getColumnIndexOrThrow(_cursor, "profile");
          final int _cursorIndexOfComment = CursorUtil.getColumnIndexOrThrow(_cursor, "comment");
          final int _cursorIndexOfLocation = CursorUtil.getColumnIndexOrThrow(_cursor, "location");
          final int _cursorIndexOfLimitBytes = CursorUtil.getColumnIndexOrThrow(_cursor, "limit_bytes");
          final int _cursorIndexOfLimitUnit = CursorUtil.getColumnIndexOrThrow(_cursor, "limit_unit");
          final int _cursorIndexOfDays = CursorUtil.getColumnIndexOrThrow(_cursor, "days");
          final int _cursorIndexOfEmailTemplate = CursorUtil.getColumnIndexOrThrow(_cursor, "email_template");
          final int _cursorIndexOfPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "price");
          final int _cursorIndexOfCreatedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "created_date");
          final int _cursorIndexOfUpdatedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_date");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "is_active");
          final int _cursorIndexOfSerialNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "serial_number");
          final int _cursorIndexOfUptimeLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "uptime_limit");
          final int _cursorIndexOfRateLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "rate_limit");
          final List<HotspotCredential> _result = new ArrayList<HotspotCredential>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final HotspotCredential _item;
            _item = new HotspotCredential();
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            _item.setId(_tmpId);
            final String _tmpUsername;
            if (_cursor.isNull(_cursorIndexOfUsername)) {
              _tmpUsername = null;
            } else {
              _tmpUsername = _cursor.getString(_cursorIndexOfUsername);
            }
            _item.setUsername(_tmpUsername);
            final String _tmpPassword;
            if (_cursor.isNull(_cursorIndexOfPassword)) {
              _tmpPassword = null;
            } else {
              _tmpPassword = _cursor.getString(_cursorIndexOfPassword);
            }
            _item.setPassword(_tmpPassword);
            final String _tmpProfile;
            if (_cursor.isNull(_cursorIndexOfProfile)) {
              _tmpProfile = null;
            } else {
              _tmpProfile = _cursor.getString(_cursorIndexOfProfile);
            }
            _item.setProfile(_tmpProfile);
            final String _tmpComment;
            if (_cursor.isNull(_cursorIndexOfComment)) {
              _tmpComment = null;
            } else {
              _tmpComment = _cursor.getString(_cursorIndexOfComment);
            }
            _item.setComment(_tmpComment);
            final String _tmpLocation;
            if (_cursor.isNull(_cursorIndexOfLocation)) {
              _tmpLocation = null;
            } else {
              _tmpLocation = _cursor.getString(_cursorIndexOfLocation);
            }
            _item.setLocation(_tmpLocation);
            final String _tmpLimitBytes;
            if (_cursor.isNull(_cursorIndexOfLimitBytes)) {
              _tmpLimitBytes = null;
            } else {
              _tmpLimitBytes = _cursor.getString(_cursorIndexOfLimitBytes);
            }
            _item.setLimitBytes(_tmpLimitBytes);
            final String _tmpLimitUnit;
            if (_cursor.isNull(_cursorIndexOfLimitUnit)) {
              _tmpLimitUnit = null;
            } else {
              _tmpLimitUnit = _cursor.getString(_cursorIndexOfLimitUnit);
            }
            _item.setLimitUnit(_tmpLimitUnit);
            final String _tmpDays;
            if (_cursor.isNull(_cursorIndexOfDays)) {
              _tmpDays = null;
            } else {
              _tmpDays = _cursor.getString(_cursorIndexOfDays);
            }
            _item.setDays(_tmpDays);
            final String _tmpEmailTemplate;
            if (_cursor.isNull(_cursorIndexOfEmailTemplate)) {
              _tmpEmailTemplate = null;
            } else {
              _tmpEmailTemplate = _cursor.getString(_cursorIndexOfEmailTemplate);
            }
            _item.setEmailTemplate(_tmpEmailTemplate);
            final String _tmpPrice;
            if (_cursor.isNull(_cursorIndexOfPrice)) {
              _tmpPrice = null;
            } else {
              _tmpPrice = _cursor.getString(_cursorIndexOfPrice);
            }
            _item.setPrice(_tmpPrice);
            final Date _tmpCreatedDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfCreatedDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfCreatedDate);
            }
            _tmpCreatedDate = Converters.fromTimestamp(_tmp);
            _item.setCreatedDate(_tmpCreatedDate);
            final Date _tmpUpdatedDate;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfUpdatedDate)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfUpdatedDate);
            }
            _tmpUpdatedDate = Converters.fromTimestamp(_tmp_1);
            _item.setUpdatedDate(_tmpUpdatedDate);
            final boolean _tmpIsActive;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp_2 != 0;
            _item.setActive(_tmpIsActive);
            final int _tmpSerialNumber;
            _tmpSerialNumber = _cursor.getInt(_cursorIndexOfSerialNumber);
            _item.setSerialNumber(_tmpSerialNumber);
            final String _tmpUptimeLimit;
            if (_cursor.isNull(_cursorIndexOfUptimeLimit)) {
              _tmpUptimeLimit = null;
            } else {
              _tmpUptimeLimit = _cursor.getString(_cursorIndexOfUptimeLimit);
            }
            _item.setUptimeLimit(_tmpUptimeLimit);
            final String _tmpRateLimit;
            if (_cursor.isNull(_cursorIndexOfRateLimit)) {
              _tmpRateLimit = null;
            } else {
              _tmpRateLimit = _cursor.getString(_cursorIndexOfRateLimit);
            }
            _item.setRateLimit(_tmpRateLimit);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public LiveData<List<HotspotCredential>> getActiveCredentials() {
    final String _sql = "SELECT * FROM hotspot_credentials WHERE is_active = 1 ORDER BY created_date DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return __db.getInvalidationTracker().createLiveData(new String[] {"hotspot_credentials"}, false, new Callable<List<HotspotCredential>>() {
      @Override
      @Nullable
      public List<HotspotCredential> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUsername = CursorUtil.getColumnIndexOrThrow(_cursor, "username");
          final int _cursorIndexOfPassword = CursorUtil.getColumnIndexOrThrow(_cursor, "password");
          final int _cursorIndexOfProfile = CursorUtil.getColumnIndexOrThrow(_cursor, "profile");
          final int _cursorIndexOfComment = CursorUtil.getColumnIndexOrThrow(_cursor, "comment");
          final int _cursorIndexOfLocation = CursorUtil.getColumnIndexOrThrow(_cursor, "location");
          final int _cursorIndexOfLimitBytes = CursorUtil.getColumnIndexOrThrow(_cursor, "limit_bytes");
          final int _cursorIndexOfLimitUnit = CursorUtil.getColumnIndexOrThrow(_cursor, "limit_unit");
          final int _cursorIndexOfDays = CursorUtil.getColumnIndexOrThrow(_cursor, "days");
          final int _cursorIndexOfEmailTemplate = CursorUtil.getColumnIndexOrThrow(_cursor, "email_template");
          final int _cursorIndexOfPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "price");
          final int _cursorIndexOfCreatedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "created_date");
          final int _cursorIndexOfUpdatedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_date");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "is_active");
          final int _cursorIndexOfSerialNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "serial_number");
          final int _cursorIndexOfUptimeLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "uptime_limit");
          final int _cursorIndexOfRateLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "rate_limit");
          final List<HotspotCredential> _result = new ArrayList<HotspotCredential>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final HotspotCredential _item;
            _item = new HotspotCredential();
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            _item.setId(_tmpId);
            final String _tmpUsername;
            if (_cursor.isNull(_cursorIndexOfUsername)) {
              _tmpUsername = null;
            } else {
              _tmpUsername = _cursor.getString(_cursorIndexOfUsername);
            }
            _item.setUsername(_tmpUsername);
            final String _tmpPassword;
            if (_cursor.isNull(_cursorIndexOfPassword)) {
              _tmpPassword = null;
            } else {
              _tmpPassword = _cursor.getString(_cursorIndexOfPassword);
            }
            _item.setPassword(_tmpPassword);
            final String _tmpProfile;
            if (_cursor.isNull(_cursorIndexOfProfile)) {
              _tmpProfile = null;
            } else {
              _tmpProfile = _cursor.getString(_cursorIndexOfProfile);
            }
            _item.setProfile(_tmpProfile);
            final String _tmpComment;
            if (_cursor.isNull(_cursorIndexOfComment)) {
              _tmpComment = null;
            } else {
              _tmpComment = _cursor.getString(_cursorIndexOfComment);
            }
            _item.setComment(_tmpComment);
            final String _tmpLocation;
            if (_cursor.isNull(_cursorIndexOfLocation)) {
              _tmpLocation = null;
            } else {
              _tmpLocation = _cursor.getString(_cursorIndexOfLocation);
            }
            _item.setLocation(_tmpLocation);
            final String _tmpLimitBytes;
            if (_cursor.isNull(_cursorIndexOfLimitBytes)) {
              _tmpLimitBytes = null;
            } else {
              _tmpLimitBytes = _cursor.getString(_cursorIndexOfLimitBytes);
            }
            _item.setLimitBytes(_tmpLimitBytes);
            final String _tmpLimitUnit;
            if (_cursor.isNull(_cursorIndexOfLimitUnit)) {
              _tmpLimitUnit = null;
            } else {
              _tmpLimitUnit = _cursor.getString(_cursorIndexOfLimitUnit);
            }
            _item.setLimitUnit(_tmpLimitUnit);
            final String _tmpDays;
            if (_cursor.isNull(_cursorIndexOfDays)) {
              _tmpDays = null;
            } else {
              _tmpDays = _cursor.getString(_cursorIndexOfDays);
            }
            _item.setDays(_tmpDays);
            final String _tmpEmailTemplate;
            if (_cursor.isNull(_cursorIndexOfEmailTemplate)) {
              _tmpEmailTemplate = null;
            } else {
              _tmpEmailTemplate = _cursor.getString(_cursorIndexOfEmailTemplate);
            }
            _item.setEmailTemplate(_tmpEmailTemplate);
            final String _tmpPrice;
            if (_cursor.isNull(_cursorIndexOfPrice)) {
              _tmpPrice = null;
            } else {
              _tmpPrice = _cursor.getString(_cursorIndexOfPrice);
            }
            _item.setPrice(_tmpPrice);
            final Date _tmpCreatedDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfCreatedDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfCreatedDate);
            }
            _tmpCreatedDate = Converters.fromTimestamp(_tmp);
            _item.setCreatedDate(_tmpCreatedDate);
            final Date _tmpUpdatedDate;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfUpdatedDate)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfUpdatedDate);
            }
            _tmpUpdatedDate = Converters.fromTimestamp(_tmp_1);
            _item.setUpdatedDate(_tmpUpdatedDate);
            final boolean _tmpIsActive;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp_2 != 0;
            _item.setActive(_tmpIsActive);
            final int _tmpSerialNumber;
            _tmpSerialNumber = _cursor.getInt(_cursorIndexOfSerialNumber);
            _item.setSerialNumber(_tmpSerialNumber);
            final String _tmpUptimeLimit;
            if (_cursor.isNull(_cursorIndexOfUptimeLimit)) {
              _tmpUptimeLimit = null;
            } else {
              _tmpUptimeLimit = _cursor.getString(_cursorIndexOfUptimeLimit);
            }
            _item.setUptimeLimit(_tmpUptimeLimit);
            final String _tmpRateLimit;
            if (_cursor.isNull(_cursorIndexOfRateLimit)) {
              _tmpRateLimit = null;
            } else {
              _tmpRateLimit = _cursor.getString(_cursorIndexOfRateLimit);
            }
            _item.setRateLimit(_tmpRateLimit);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public LiveData<HotspotCredential> getCredentialById(final int id) {
    final String _sql = "SELECT * FROM hotspot_credentials WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, id);
    return __db.getInvalidationTracker().createLiveData(new String[] {"hotspot_credentials"}, false, new Callable<HotspotCredential>() {
      @Override
      @Nullable
      public HotspotCredential call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUsername = CursorUtil.getColumnIndexOrThrow(_cursor, "username");
          final int _cursorIndexOfPassword = CursorUtil.getColumnIndexOrThrow(_cursor, "password");
          final int _cursorIndexOfProfile = CursorUtil.getColumnIndexOrThrow(_cursor, "profile");
          final int _cursorIndexOfComment = CursorUtil.getColumnIndexOrThrow(_cursor, "comment");
          final int _cursorIndexOfLocation = CursorUtil.getColumnIndexOrThrow(_cursor, "location");
          final int _cursorIndexOfLimitBytes = CursorUtil.getColumnIndexOrThrow(_cursor, "limit_bytes");
          final int _cursorIndexOfLimitUnit = CursorUtil.getColumnIndexOrThrow(_cursor, "limit_unit");
          final int _cursorIndexOfDays = CursorUtil.getColumnIndexOrThrow(_cursor, "days");
          final int _cursorIndexOfEmailTemplate = CursorUtil.getColumnIndexOrThrow(_cursor, "email_template");
          final int _cursorIndexOfPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "price");
          final int _cursorIndexOfCreatedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "created_date");
          final int _cursorIndexOfUpdatedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_date");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "is_active");
          final int _cursorIndexOfSerialNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "serial_number");
          final int _cursorIndexOfUptimeLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "uptime_limit");
          final int _cursorIndexOfRateLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "rate_limit");
          final HotspotCredential _result;
          if (_cursor.moveToFirst()) {
            _result = new HotspotCredential();
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            _result.setId(_tmpId);
            final String _tmpUsername;
            if (_cursor.isNull(_cursorIndexOfUsername)) {
              _tmpUsername = null;
            } else {
              _tmpUsername = _cursor.getString(_cursorIndexOfUsername);
            }
            _result.setUsername(_tmpUsername);
            final String _tmpPassword;
            if (_cursor.isNull(_cursorIndexOfPassword)) {
              _tmpPassword = null;
            } else {
              _tmpPassword = _cursor.getString(_cursorIndexOfPassword);
            }
            _result.setPassword(_tmpPassword);
            final String _tmpProfile;
            if (_cursor.isNull(_cursorIndexOfProfile)) {
              _tmpProfile = null;
            } else {
              _tmpProfile = _cursor.getString(_cursorIndexOfProfile);
            }
            _result.setProfile(_tmpProfile);
            final String _tmpComment;
            if (_cursor.isNull(_cursorIndexOfComment)) {
              _tmpComment = null;
            } else {
              _tmpComment = _cursor.getString(_cursorIndexOfComment);
            }
            _result.setComment(_tmpComment);
            final String _tmpLocation;
            if (_cursor.isNull(_cursorIndexOfLocation)) {
              _tmpLocation = null;
            } else {
              _tmpLocation = _cursor.getString(_cursorIndexOfLocation);
            }
            _result.setLocation(_tmpLocation);
            final String _tmpLimitBytes;
            if (_cursor.isNull(_cursorIndexOfLimitBytes)) {
              _tmpLimitBytes = null;
            } else {
              _tmpLimitBytes = _cursor.getString(_cursorIndexOfLimitBytes);
            }
            _result.setLimitBytes(_tmpLimitBytes);
            final String _tmpLimitUnit;
            if (_cursor.isNull(_cursorIndexOfLimitUnit)) {
              _tmpLimitUnit = null;
            } else {
              _tmpLimitUnit = _cursor.getString(_cursorIndexOfLimitUnit);
            }
            _result.setLimitUnit(_tmpLimitUnit);
            final String _tmpDays;
            if (_cursor.isNull(_cursorIndexOfDays)) {
              _tmpDays = null;
            } else {
              _tmpDays = _cursor.getString(_cursorIndexOfDays);
            }
            _result.setDays(_tmpDays);
            final String _tmpEmailTemplate;
            if (_cursor.isNull(_cursorIndexOfEmailTemplate)) {
              _tmpEmailTemplate = null;
            } else {
              _tmpEmailTemplate = _cursor.getString(_cursorIndexOfEmailTemplate);
            }
            _result.setEmailTemplate(_tmpEmailTemplate);
            final String _tmpPrice;
            if (_cursor.isNull(_cursorIndexOfPrice)) {
              _tmpPrice = null;
            } else {
              _tmpPrice = _cursor.getString(_cursorIndexOfPrice);
            }
            _result.setPrice(_tmpPrice);
            final Date _tmpCreatedDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfCreatedDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfCreatedDate);
            }
            _tmpCreatedDate = Converters.fromTimestamp(_tmp);
            _result.setCreatedDate(_tmpCreatedDate);
            final Date _tmpUpdatedDate;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfUpdatedDate)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfUpdatedDate);
            }
            _tmpUpdatedDate = Converters.fromTimestamp(_tmp_1);
            _result.setUpdatedDate(_tmpUpdatedDate);
            final boolean _tmpIsActive;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp_2 != 0;
            _result.setActive(_tmpIsActive);
            final int _tmpSerialNumber;
            _tmpSerialNumber = _cursor.getInt(_cursorIndexOfSerialNumber);
            _result.setSerialNumber(_tmpSerialNumber);
            final String _tmpUptimeLimit;
            if (_cursor.isNull(_cursorIndexOfUptimeLimit)) {
              _tmpUptimeLimit = null;
            } else {
              _tmpUptimeLimit = _cursor.getString(_cursorIndexOfUptimeLimit);
            }
            _result.setUptimeLimit(_tmpUptimeLimit);
            final String _tmpRateLimit;
            if (_cursor.isNull(_cursorIndexOfRateLimit)) {
              _tmpRateLimit = null;
            } else {
              _tmpRateLimit = _cursor.getString(_cursorIndexOfRateLimit);
            }
            _result.setRateLimit(_tmpRateLimit);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public HotspotCredential getCredentialByUsername(final String username) {
    final String _sql = "SELECT * FROM hotspot_credentials WHERE username = ? LIMIT 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (username == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, username);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfUsername = CursorUtil.getColumnIndexOrThrow(_cursor, "username");
      final int _cursorIndexOfPassword = CursorUtil.getColumnIndexOrThrow(_cursor, "password");
      final int _cursorIndexOfProfile = CursorUtil.getColumnIndexOrThrow(_cursor, "profile");
      final int _cursorIndexOfComment = CursorUtil.getColumnIndexOrThrow(_cursor, "comment");
      final int _cursorIndexOfLocation = CursorUtil.getColumnIndexOrThrow(_cursor, "location");
      final int _cursorIndexOfLimitBytes = CursorUtil.getColumnIndexOrThrow(_cursor, "limit_bytes");
      final int _cursorIndexOfLimitUnit = CursorUtil.getColumnIndexOrThrow(_cursor, "limit_unit");
      final int _cursorIndexOfDays = CursorUtil.getColumnIndexOrThrow(_cursor, "days");
      final int _cursorIndexOfEmailTemplate = CursorUtil.getColumnIndexOrThrow(_cursor, "email_template");
      final int _cursorIndexOfPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "price");
      final int _cursorIndexOfCreatedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "created_date");
      final int _cursorIndexOfUpdatedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_date");
      final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "is_active");
      final int _cursorIndexOfSerialNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "serial_number");
      final int _cursorIndexOfUptimeLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "uptime_limit");
      final int _cursorIndexOfRateLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "rate_limit");
      final HotspotCredential _result;
      if (_cursor.moveToFirst()) {
        _result = new HotspotCredential();
        final int _tmpId;
        _tmpId = _cursor.getInt(_cursorIndexOfId);
        _result.setId(_tmpId);
        final String _tmpUsername;
        if (_cursor.isNull(_cursorIndexOfUsername)) {
          _tmpUsername = null;
        } else {
          _tmpUsername = _cursor.getString(_cursorIndexOfUsername);
        }
        _result.setUsername(_tmpUsername);
        final String _tmpPassword;
        if (_cursor.isNull(_cursorIndexOfPassword)) {
          _tmpPassword = null;
        } else {
          _tmpPassword = _cursor.getString(_cursorIndexOfPassword);
        }
        _result.setPassword(_tmpPassword);
        final String _tmpProfile;
        if (_cursor.isNull(_cursorIndexOfProfile)) {
          _tmpProfile = null;
        } else {
          _tmpProfile = _cursor.getString(_cursorIndexOfProfile);
        }
        _result.setProfile(_tmpProfile);
        final String _tmpComment;
        if (_cursor.isNull(_cursorIndexOfComment)) {
          _tmpComment = null;
        } else {
          _tmpComment = _cursor.getString(_cursorIndexOfComment);
        }
        _result.setComment(_tmpComment);
        final String _tmpLocation;
        if (_cursor.isNull(_cursorIndexOfLocation)) {
          _tmpLocation = null;
        } else {
          _tmpLocation = _cursor.getString(_cursorIndexOfLocation);
        }
        _result.setLocation(_tmpLocation);
        final String _tmpLimitBytes;
        if (_cursor.isNull(_cursorIndexOfLimitBytes)) {
          _tmpLimitBytes = null;
        } else {
          _tmpLimitBytes = _cursor.getString(_cursorIndexOfLimitBytes);
        }
        _result.setLimitBytes(_tmpLimitBytes);
        final String _tmpLimitUnit;
        if (_cursor.isNull(_cursorIndexOfLimitUnit)) {
          _tmpLimitUnit = null;
        } else {
          _tmpLimitUnit = _cursor.getString(_cursorIndexOfLimitUnit);
        }
        _result.setLimitUnit(_tmpLimitUnit);
        final String _tmpDays;
        if (_cursor.isNull(_cursorIndexOfDays)) {
          _tmpDays = null;
        } else {
          _tmpDays = _cursor.getString(_cursorIndexOfDays);
        }
        _result.setDays(_tmpDays);
        final String _tmpEmailTemplate;
        if (_cursor.isNull(_cursorIndexOfEmailTemplate)) {
          _tmpEmailTemplate = null;
        } else {
          _tmpEmailTemplate = _cursor.getString(_cursorIndexOfEmailTemplate);
        }
        _result.setEmailTemplate(_tmpEmailTemplate);
        final String _tmpPrice;
        if (_cursor.isNull(_cursorIndexOfPrice)) {
          _tmpPrice = null;
        } else {
          _tmpPrice = _cursor.getString(_cursorIndexOfPrice);
        }
        _result.setPrice(_tmpPrice);
        final Date _tmpCreatedDate;
        final Long _tmp;
        if (_cursor.isNull(_cursorIndexOfCreatedDate)) {
          _tmp = null;
        } else {
          _tmp = _cursor.getLong(_cursorIndexOfCreatedDate);
        }
        _tmpCreatedDate = Converters.fromTimestamp(_tmp);
        _result.setCreatedDate(_tmpCreatedDate);
        final Date _tmpUpdatedDate;
        final Long _tmp_1;
        if (_cursor.isNull(_cursorIndexOfUpdatedDate)) {
          _tmp_1 = null;
        } else {
          _tmp_1 = _cursor.getLong(_cursorIndexOfUpdatedDate);
        }
        _tmpUpdatedDate = Converters.fromTimestamp(_tmp_1);
        _result.setUpdatedDate(_tmpUpdatedDate);
        final boolean _tmpIsActive;
        final int _tmp_2;
        _tmp_2 = _cursor.getInt(_cursorIndexOfIsActive);
        _tmpIsActive = _tmp_2 != 0;
        _result.setActive(_tmpIsActive);
        final int _tmpSerialNumber;
        _tmpSerialNumber = _cursor.getInt(_cursorIndexOfSerialNumber);
        _result.setSerialNumber(_tmpSerialNumber);
        final String _tmpUptimeLimit;
        if (_cursor.isNull(_cursorIndexOfUptimeLimit)) {
          _tmpUptimeLimit = null;
        } else {
          _tmpUptimeLimit = _cursor.getString(_cursorIndexOfUptimeLimit);
        }
        _result.setUptimeLimit(_tmpUptimeLimit);
        final String _tmpRateLimit;
        if (_cursor.isNull(_cursorIndexOfRateLimit)) {
          _tmpRateLimit = null;
        } else {
          _tmpRateLimit = _cursor.getString(_cursorIndexOfRateLimit);
        }
        _result.setRateLimit(_tmpRateLimit);
      } else {
        _result = null;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public LiveData<List<HotspotCredential>> getCredentialsByProfile(final String profile) {
    final String _sql = "SELECT * FROM hotspot_credentials WHERE profile = ? ORDER BY created_date DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (profile == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, profile);
    }
    return __db.getInvalidationTracker().createLiveData(new String[] {"hotspot_credentials"}, false, new Callable<List<HotspotCredential>>() {
      @Override
      @Nullable
      public List<HotspotCredential> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUsername = CursorUtil.getColumnIndexOrThrow(_cursor, "username");
          final int _cursorIndexOfPassword = CursorUtil.getColumnIndexOrThrow(_cursor, "password");
          final int _cursorIndexOfProfile = CursorUtil.getColumnIndexOrThrow(_cursor, "profile");
          final int _cursorIndexOfComment = CursorUtil.getColumnIndexOrThrow(_cursor, "comment");
          final int _cursorIndexOfLocation = CursorUtil.getColumnIndexOrThrow(_cursor, "location");
          final int _cursorIndexOfLimitBytes = CursorUtil.getColumnIndexOrThrow(_cursor, "limit_bytes");
          final int _cursorIndexOfLimitUnit = CursorUtil.getColumnIndexOrThrow(_cursor, "limit_unit");
          final int _cursorIndexOfDays = CursorUtil.getColumnIndexOrThrow(_cursor, "days");
          final int _cursorIndexOfEmailTemplate = CursorUtil.getColumnIndexOrThrow(_cursor, "email_template");
          final int _cursorIndexOfPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "price");
          final int _cursorIndexOfCreatedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "created_date");
          final int _cursorIndexOfUpdatedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_date");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "is_active");
          final int _cursorIndexOfSerialNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "serial_number");
          final int _cursorIndexOfUptimeLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "uptime_limit");
          final int _cursorIndexOfRateLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "rate_limit");
          final List<HotspotCredential> _result = new ArrayList<HotspotCredential>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final HotspotCredential _item;
            _item = new HotspotCredential();
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            _item.setId(_tmpId);
            final String _tmpUsername;
            if (_cursor.isNull(_cursorIndexOfUsername)) {
              _tmpUsername = null;
            } else {
              _tmpUsername = _cursor.getString(_cursorIndexOfUsername);
            }
            _item.setUsername(_tmpUsername);
            final String _tmpPassword;
            if (_cursor.isNull(_cursorIndexOfPassword)) {
              _tmpPassword = null;
            } else {
              _tmpPassword = _cursor.getString(_cursorIndexOfPassword);
            }
            _item.setPassword(_tmpPassword);
            final String _tmpProfile;
            if (_cursor.isNull(_cursorIndexOfProfile)) {
              _tmpProfile = null;
            } else {
              _tmpProfile = _cursor.getString(_cursorIndexOfProfile);
            }
            _item.setProfile(_tmpProfile);
            final String _tmpComment;
            if (_cursor.isNull(_cursorIndexOfComment)) {
              _tmpComment = null;
            } else {
              _tmpComment = _cursor.getString(_cursorIndexOfComment);
            }
            _item.setComment(_tmpComment);
            final String _tmpLocation;
            if (_cursor.isNull(_cursorIndexOfLocation)) {
              _tmpLocation = null;
            } else {
              _tmpLocation = _cursor.getString(_cursorIndexOfLocation);
            }
            _item.setLocation(_tmpLocation);
            final String _tmpLimitBytes;
            if (_cursor.isNull(_cursorIndexOfLimitBytes)) {
              _tmpLimitBytes = null;
            } else {
              _tmpLimitBytes = _cursor.getString(_cursorIndexOfLimitBytes);
            }
            _item.setLimitBytes(_tmpLimitBytes);
            final String _tmpLimitUnit;
            if (_cursor.isNull(_cursorIndexOfLimitUnit)) {
              _tmpLimitUnit = null;
            } else {
              _tmpLimitUnit = _cursor.getString(_cursorIndexOfLimitUnit);
            }
            _item.setLimitUnit(_tmpLimitUnit);
            final String _tmpDays;
            if (_cursor.isNull(_cursorIndexOfDays)) {
              _tmpDays = null;
            } else {
              _tmpDays = _cursor.getString(_cursorIndexOfDays);
            }
            _item.setDays(_tmpDays);
            final String _tmpEmailTemplate;
            if (_cursor.isNull(_cursorIndexOfEmailTemplate)) {
              _tmpEmailTemplate = null;
            } else {
              _tmpEmailTemplate = _cursor.getString(_cursorIndexOfEmailTemplate);
            }
            _item.setEmailTemplate(_tmpEmailTemplate);
            final String _tmpPrice;
            if (_cursor.isNull(_cursorIndexOfPrice)) {
              _tmpPrice = null;
            } else {
              _tmpPrice = _cursor.getString(_cursorIndexOfPrice);
            }
            _item.setPrice(_tmpPrice);
            final Date _tmpCreatedDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfCreatedDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfCreatedDate);
            }
            _tmpCreatedDate = Converters.fromTimestamp(_tmp);
            _item.setCreatedDate(_tmpCreatedDate);
            final Date _tmpUpdatedDate;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfUpdatedDate)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfUpdatedDate);
            }
            _tmpUpdatedDate = Converters.fromTimestamp(_tmp_1);
            _item.setUpdatedDate(_tmpUpdatedDate);
            final boolean _tmpIsActive;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp_2 != 0;
            _item.setActive(_tmpIsActive);
            final int _tmpSerialNumber;
            _tmpSerialNumber = _cursor.getInt(_cursorIndexOfSerialNumber);
            _item.setSerialNumber(_tmpSerialNumber);
            final String _tmpUptimeLimit;
            if (_cursor.isNull(_cursorIndexOfUptimeLimit)) {
              _tmpUptimeLimit = null;
            } else {
              _tmpUptimeLimit = _cursor.getString(_cursorIndexOfUptimeLimit);
            }
            _item.setUptimeLimit(_tmpUptimeLimit);
            final String _tmpRateLimit;
            if (_cursor.isNull(_cursorIndexOfRateLimit)) {
              _tmpRateLimit = null;
            } else {
              _tmpRateLimit = _cursor.getString(_cursorIndexOfRateLimit);
            }
            _item.setRateLimit(_tmpRateLimit);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public LiveData<List<String>> getAllProfiles() {
    final String _sql = "SELECT DISTINCT profile FROM hotspot_credentials WHERE profile IS NOT NULL AND profile != ''";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return __db.getInvalidationTracker().createLiveData(new String[] {"hotspot_credentials"}, false, new Callable<List<String>>() {
      @Override
      @Nullable
      public List<String> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final List<String> _result = new ArrayList<String>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final String _item;
            if (_cursor.isNull(0)) {
              _item = null;
            } else {
              _item = _cursor.getString(0);
            }
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public LiveData<Integer> getCredentialsCount() {
    final String _sql = "SELECT COUNT(*) FROM hotspot_credentials";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return __db.getInvalidationTracker().createLiveData(new String[] {"hotspot_credentials"}, false, new Callable<Integer>() {
      @Override
      @Nullable
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public LiveData<Integer> getActiveCredentialsCount() {
    final String _sql = "SELECT COUNT(*) FROM hotspot_credentials WHERE is_active = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return __db.getInvalidationTracker().createLiveData(new String[] {"hotspot_credentials"}, false, new Callable<Integer>() {
      @Override
      @Nullable
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public int getLastSerialNumber() {
    final String _sql = "SELECT MAX(serial_number) FROM hotspot_credentials";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _result;
      if (_cursor.moveToFirst()) {
        _result = _cursor.getInt(0);
      } else {
        _result = 0;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public LiveData<List<HotspotCredential>> getCredentialsByDateRange(final long startDate,
      final long endDate) {
    final String _sql = "SELECT * FROM hotspot_credentials WHERE created_date BETWEEN ? AND ? ORDER BY created_date DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, startDate);
    _argIndex = 2;
    _statement.bindLong(_argIndex, endDate);
    return __db.getInvalidationTracker().createLiveData(new String[] {"hotspot_credentials"}, false, new Callable<List<HotspotCredential>>() {
      @Override
      @Nullable
      public List<HotspotCredential> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUsername = CursorUtil.getColumnIndexOrThrow(_cursor, "username");
          final int _cursorIndexOfPassword = CursorUtil.getColumnIndexOrThrow(_cursor, "password");
          final int _cursorIndexOfProfile = CursorUtil.getColumnIndexOrThrow(_cursor, "profile");
          final int _cursorIndexOfComment = CursorUtil.getColumnIndexOrThrow(_cursor, "comment");
          final int _cursorIndexOfLocation = CursorUtil.getColumnIndexOrThrow(_cursor, "location");
          final int _cursorIndexOfLimitBytes = CursorUtil.getColumnIndexOrThrow(_cursor, "limit_bytes");
          final int _cursorIndexOfLimitUnit = CursorUtil.getColumnIndexOrThrow(_cursor, "limit_unit");
          final int _cursorIndexOfDays = CursorUtil.getColumnIndexOrThrow(_cursor, "days");
          final int _cursorIndexOfEmailTemplate = CursorUtil.getColumnIndexOrThrow(_cursor, "email_template");
          final int _cursorIndexOfPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "price");
          final int _cursorIndexOfCreatedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "created_date");
          final int _cursorIndexOfUpdatedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_date");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "is_active");
          final int _cursorIndexOfSerialNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "serial_number");
          final int _cursorIndexOfUptimeLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "uptime_limit");
          final int _cursorIndexOfRateLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "rate_limit");
          final List<HotspotCredential> _result = new ArrayList<HotspotCredential>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final HotspotCredential _item;
            _item = new HotspotCredential();
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            _item.setId(_tmpId);
            final String _tmpUsername;
            if (_cursor.isNull(_cursorIndexOfUsername)) {
              _tmpUsername = null;
            } else {
              _tmpUsername = _cursor.getString(_cursorIndexOfUsername);
            }
            _item.setUsername(_tmpUsername);
            final String _tmpPassword;
            if (_cursor.isNull(_cursorIndexOfPassword)) {
              _tmpPassword = null;
            } else {
              _tmpPassword = _cursor.getString(_cursorIndexOfPassword);
            }
            _item.setPassword(_tmpPassword);
            final String _tmpProfile;
            if (_cursor.isNull(_cursorIndexOfProfile)) {
              _tmpProfile = null;
            } else {
              _tmpProfile = _cursor.getString(_cursorIndexOfProfile);
            }
            _item.setProfile(_tmpProfile);
            final String _tmpComment;
            if (_cursor.isNull(_cursorIndexOfComment)) {
              _tmpComment = null;
            } else {
              _tmpComment = _cursor.getString(_cursorIndexOfComment);
            }
            _item.setComment(_tmpComment);
            final String _tmpLocation;
            if (_cursor.isNull(_cursorIndexOfLocation)) {
              _tmpLocation = null;
            } else {
              _tmpLocation = _cursor.getString(_cursorIndexOfLocation);
            }
            _item.setLocation(_tmpLocation);
            final String _tmpLimitBytes;
            if (_cursor.isNull(_cursorIndexOfLimitBytes)) {
              _tmpLimitBytes = null;
            } else {
              _tmpLimitBytes = _cursor.getString(_cursorIndexOfLimitBytes);
            }
            _item.setLimitBytes(_tmpLimitBytes);
            final String _tmpLimitUnit;
            if (_cursor.isNull(_cursorIndexOfLimitUnit)) {
              _tmpLimitUnit = null;
            } else {
              _tmpLimitUnit = _cursor.getString(_cursorIndexOfLimitUnit);
            }
            _item.setLimitUnit(_tmpLimitUnit);
            final String _tmpDays;
            if (_cursor.isNull(_cursorIndexOfDays)) {
              _tmpDays = null;
            } else {
              _tmpDays = _cursor.getString(_cursorIndexOfDays);
            }
            _item.setDays(_tmpDays);
            final String _tmpEmailTemplate;
            if (_cursor.isNull(_cursorIndexOfEmailTemplate)) {
              _tmpEmailTemplate = null;
            } else {
              _tmpEmailTemplate = _cursor.getString(_cursorIndexOfEmailTemplate);
            }
            _item.setEmailTemplate(_tmpEmailTemplate);
            final String _tmpPrice;
            if (_cursor.isNull(_cursorIndexOfPrice)) {
              _tmpPrice = null;
            } else {
              _tmpPrice = _cursor.getString(_cursorIndexOfPrice);
            }
            _item.setPrice(_tmpPrice);
            final Date _tmpCreatedDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfCreatedDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfCreatedDate);
            }
            _tmpCreatedDate = Converters.fromTimestamp(_tmp);
            _item.setCreatedDate(_tmpCreatedDate);
            final Date _tmpUpdatedDate;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfUpdatedDate)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfUpdatedDate);
            }
            _tmpUpdatedDate = Converters.fromTimestamp(_tmp_1);
            _item.setUpdatedDate(_tmpUpdatedDate);
            final boolean _tmpIsActive;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp_2 != 0;
            _item.setActive(_tmpIsActive);
            final int _tmpSerialNumber;
            _tmpSerialNumber = _cursor.getInt(_cursorIndexOfSerialNumber);
            _item.setSerialNumber(_tmpSerialNumber);
            final String _tmpUptimeLimit;
            if (_cursor.isNull(_cursorIndexOfUptimeLimit)) {
              _tmpUptimeLimit = null;
            } else {
              _tmpUptimeLimit = _cursor.getString(_cursorIndexOfUptimeLimit);
            }
            _item.setUptimeLimit(_tmpUptimeLimit);
            final String _tmpRateLimit;
            if (_cursor.isNull(_cursorIndexOfRateLimit)) {
              _tmpRateLimit = null;
            } else {
              _tmpRateLimit = _cursor.getString(_cursorIndexOfRateLimit);
            }
            _item.setRateLimit(_tmpRateLimit);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public LiveData<List<HotspotCredential>> searchCredentials(final String searchQuery) {
    final String _sql = "SELECT * FROM hotspot_credentials WHERE username LIKE ? OR comment LIKE ? OR location LIKE ? ORDER BY created_date DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 3);
    int _argIndex = 1;
    if (searchQuery == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, searchQuery);
    }
    _argIndex = 2;
    if (searchQuery == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, searchQuery);
    }
    _argIndex = 3;
    if (searchQuery == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, searchQuery);
    }
    return __db.getInvalidationTracker().createLiveData(new String[] {"hotspot_credentials"}, false, new Callable<List<HotspotCredential>>() {
      @Override
      @Nullable
      public List<HotspotCredential> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUsername = CursorUtil.getColumnIndexOrThrow(_cursor, "username");
          final int _cursorIndexOfPassword = CursorUtil.getColumnIndexOrThrow(_cursor, "password");
          final int _cursorIndexOfProfile = CursorUtil.getColumnIndexOrThrow(_cursor, "profile");
          final int _cursorIndexOfComment = CursorUtil.getColumnIndexOrThrow(_cursor, "comment");
          final int _cursorIndexOfLocation = CursorUtil.getColumnIndexOrThrow(_cursor, "location");
          final int _cursorIndexOfLimitBytes = CursorUtil.getColumnIndexOrThrow(_cursor, "limit_bytes");
          final int _cursorIndexOfLimitUnit = CursorUtil.getColumnIndexOrThrow(_cursor, "limit_unit");
          final int _cursorIndexOfDays = CursorUtil.getColumnIndexOrThrow(_cursor, "days");
          final int _cursorIndexOfEmailTemplate = CursorUtil.getColumnIndexOrThrow(_cursor, "email_template");
          final int _cursorIndexOfPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "price");
          final int _cursorIndexOfCreatedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "created_date");
          final int _cursorIndexOfUpdatedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_date");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "is_active");
          final int _cursorIndexOfSerialNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "serial_number");
          final int _cursorIndexOfUptimeLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "uptime_limit");
          final int _cursorIndexOfRateLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "rate_limit");
          final List<HotspotCredential> _result = new ArrayList<HotspotCredential>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final HotspotCredential _item;
            _item = new HotspotCredential();
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            _item.setId(_tmpId);
            final String _tmpUsername;
            if (_cursor.isNull(_cursorIndexOfUsername)) {
              _tmpUsername = null;
            } else {
              _tmpUsername = _cursor.getString(_cursorIndexOfUsername);
            }
            _item.setUsername(_tmpUsername);
            final String _tmpPassword;
            if (_cursor.isNull(_cursorIndexOfPassword)) {
              _tmpPassword = null;
            } else {
              _tmpPassword = _cursor.getString(_cursorIndexOfPassword);
            }
            _item.setPassword(_tmpPassword);
            final String _tmpProfile;
            if (_cursor.isNull(_cursorIndexOfProfile)) {
              _tmpProfile = null;
            } else {
              _tmpProfile = _cursor.getString(_cursorIndexOfProfile);
            }
            _item.setProfile(_tmpProfile);
            final String _tmpComment;
            if (_cursor.isNull(_cursorIndexOfComment)) {
              _tmpComment = null;
            } else {
              _tmpComment = _cursor.getString(_cursorIndexOfComment);
            }
            _item.setComment(_tmpComment);
            final String _tmpLocation;
            if (_cursor.isNull(_cursorIndexOfLocation)) {
              _tmpLocation = null;
            } else {
              _tmpLocation = _cursor.getString(_cursorIndexOfLocation);
            }
            _item.setLocation(_tmpLocation);
            final String _tmpLimitBytes;
            if (_cursor.isNull(_cursorIndexOfLimitBytes)) {
              _tmpLimitBytes = null;
            } else {
              _tmpLimitBytes = _cursor.getString(_cursorIndexOfLimitBytes);
            }
            _item.setLimitBytes(_tmpLimitBytes);
            final String _tmpLimitUnit;
            if (_cursor.isNull(_cursorIndexOfLimitUnit)) {
              _tmpLimitUnit = null;
            } else {
              _tmpLimitUnit = _cursor.getString(_cursorIndexOfLimitUnit);
            }
            _item.setLimitUnit(_tmpLimitUnit);
            final String _tmpDays;
            if (_cursor.isNull(_cursorIndexOfDays)) {
              _tmpDays = null;
            } else {
              _tmpDays = _cursor.getString(_cursorIndexOfDays);
            }
            _item.setDays(_tmpDays);
            final String _tmpEmailTemplate;
            if (_cursor.isNull(_cursorIndexOfEmailTemplate)) {
              _tmpEmailTemplate = null;
            } else {
              _tmpEmailTemplate = _cursor.getString(_cursorIndexOfEmailTemplate);
            }
            _item.setEmailTemplate(_tmpEmailTemplate);
            final String _tmpPrice;
            if (_cursor.isNull(_cursorIndexOfPrice)) {
              _tmpPrice = null;
            } else {
              _tmpPrice = _cursor.getString(_cursorIndexOfPrice);
            }
            _item.setPrice(_tmpPrice);
            final Date _tmpCreatedDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfCreatedDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfCreatedDate);
            }
            _tmpCreatedDate = Converters.fromTimestamp(_tmp);
            _item.setCreatedDate(_tmpCreatedDate);
            final Date _tmpUpdatedDate;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfUpdatedDate)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfUpdatedDate);
            }
            _tmpUpdatedDate = Converters.fromTimestamp(_tmp_1);
            _item.setUpdatedDate(_tmpUpdatedDate);
            final boolean _tmpIsActive;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp_2 != 0;
            _item.setActive(_tmpIsActive);
            final int _tmpSerialNumber;
            _tmpSerialNumber = _cursor.getInt(_cursorIndexOfSerialNumber);
            _item.setSerialNumber(_tmpSerialNumber);
            final String _tmpUptimeLimit;
            if (_cursor.isNull(_cursorIndexOfUptimeLimit)) {
              _tmpUptimeLimit = null;
            } else {
              _tmpUptimeLimit = _cursor.getString(_cursorIndexOfUptimeLimit);
            }
            _item.setUptimeLimit(_tmpUptimeLimit);
            final String _tmpRateLimit;
            if (_cursor.isNull(_cursorIndexOfRateLimit)) {
              _tmpRateLimit = null;
            } else {
              _tmpRateLimit = _cursor.getString(_cursorIndexOfRateLimit);
            }
            _item.setRateLimit(_tmpRateLimit);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public LiveData<List<HotspotCredential>> getCredentialsByLimitUnit(final String limitUnit) {
    final String _sql = "SELECT * FROM hotspot_credentials WHERE limit_unit = ? ORDER BY created_date DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (limitUnit == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, limitUnit);
    }
    return __db.getInvalidationTracker().createLiveData(new String[] {"hotspot_credentials"}, false, new Callable<List<HotspotCredential>>() {
      @Override
      @Nullable
      public List<HotspotCredential> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUsername = CursorUtil.getColumnIndexOrThrow(_cursor, "username");
          final int _cursorIndexOfPassword = CursorUtil.getColumnIndexOrThrow(_cursor, "password");
          final int _cursorIndexOfProfile = CursorUtil.getColumnIndexOrThrow(_cursor, "profile");
          final int _cursorIndexOfComment = CursorUtil.getColumnIndexOrThrow(_cursor, "comment");
          final int _cursorIndexOfLocation = CursorUtil.getColumnIndexOrThrow(_cursor, "location");
          final int _cursorIndexOfLimitBytes = CursorUtil.getColumnIndexOrThrow(_cursor, "limit_bytes");
          final int _cursorIndexOfLimitUnit = CursorUtil.getColumnIndexOrThrow(_cursor, "limit_unit");
          final int _cursorIndexOfDays = CursorUtil.getColumnIndexOrThrow(_cursor, "days");
          final int _cursorIndexOfEmailTemplate = CursorUtil.getColumnIndexOrThrow(_cursor, "email_template");
          final int _cursorIndexOfPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "price");
          final int _cursorIndexOfCreatedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "created_date");
          final int _cursorIndexOfUpdatedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_date");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "is_active");
          final int _cursorIndexOfSerialNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "serial_number");
          final int _cursorIndexOfUptimeLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "uptime_limit");
          final int _cursorIndexOfRateLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "rate_limit");
          final List<HotspotCredential> _result = new ArrayList<HotspotCredential>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final HotspotCredential _item;
            _item = new HotspotCredential();
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            _item.setId(_tmpId);
            final String _tmpUsername;
            if (_cursor.isNull(_cursorIndexOfUsername)) {
              _tmpUsername = null;
            } else {
              _tmpUsername = _cursor.getString(_cursorIndexOfUsername);
            }
            _item.setUsername(_tmpUsername);
            final String _tmpPassword;
            if (_cursor.isNull(_cursorIndexOfPassword)) {
              _tmpPassword = null;
            } else {
              _tmpPassword = _cursor.getString(_cursorIndexOfPassword);
            }
            _item.setPassword(_tmpPassword);
            final String _tmpProfile;
            if (_cursor.isNull(_cursorIndexOfProfile)) {
              _tmpProfile = null;
            } else {
              _tmpProfile = _cursor.getString(_cursorIndexOfProfile);
            }
            _item.setProfile(_tmpProfile);
            final String _tmpComment;
            if (_cursor.isNull(_cursorIndexOfComment)) {
              _tmpComment = null;
            } else {
              _tmpComment = _cursor.getString(_cursorIndexOfComment);
            }
            _item.setComment(_tmpComment);
            final String _tmpLocation;
            if (_cursor.isNull(_cursorIndexOfLocation)) {
              _tmpLocation = null;
            } else {
              _tmpLocation = _cursor.getString(_cursorIndexOfLocation);
            }
            _item.setLocation(_tmpLocation);
            final String _tmpLimitBytes;
            if (_cursor.isNull(_cursorIndexOfLimitBytes)) {
              _tmpLimitBytes = null;
            } else {
              _tmpLimitBytes = _cursor.getString(_cursorIndexOfLimitBytes);
            }
            _item.setLimitBytes(_tmpLimitBytes);
            final String _tmpLimitUnit;
            if (_cursor.isNull(_cursorIndexOfLimitUnit)) {
              _tmpLimitUnit = null;
            } else {
              _tmpLimitUnit = _cursor.getString(_cursorIndexOfLimitUnit);
            }
            _item.setLimitUnit(_tmpLimitUnit);
            final String _tmpDays;
            if (_cursor.isNull(_cursorIndexOfDays)) {
              _tmpDays = null;
            } else {
              _tmpDays = _cursor.getString(_cursorIndexOfDays);
            }
            _item.setDays(_tmpDays);
            final String _tmpEmailTemplate;
            if (_cursor.isNull(_cursorIndexOfEmailTemplate)) {
              _tmpEmailTemplate = null;
            } else {
              _tmpEmailTemplate = _cursor.getString(_cursorIndexOfEmailTemplate);
            }
            _item.setEmailTemplate(_tmpEmailTemplate);
            final String _tmpPrice;
            if (_cursor.isNull(_cursorIndexOfPrice)) {
              _tmpPrice = null;
            } else {
              _tmpPrice = _cursor.getString(_cursorIndexOfPrice);
            }
            _item.setPrice(_tmpPrice);
            final Date _tmpCreatedDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfCreatedDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfCreatedDate);
            }
            _tmpCreatedDate = Converters.fromTimestamp(_tmp);
            _item.setCreatedDate(_tmpCreatedDate);
            final Date _tmpUpdatedDate;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfUpdatedDate)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfUpdatedDate);
            }
            _tmpUpdatedDate = Converters.fromTimestamp(_tmp_1);
            _item.setUpdatedDate(_tmpUpdatedDate);
            final boolean _tmpIsActive;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp_2 != 0;
            _item.setActive(_tmpIsActive);
            final int _tmpSerialNumber;
            _tmpSerialNumber = _cursor.getInt(_cursorIndexOfSerialNumber);
            _item.setSerialNumber(_tmpSerialNumber);
            final String _tmpUptimeLimit;
            if (_cursor.isNull(_cursorIndexOfUptimeLimit)) {
              _tmpUptimeLimit = null;
            } else {
              _tmpUptimeLimit = _cursor.getString(_cursorIndexOfUptimeLimit);
            }
            _item.setUptimeLimit(_tmpUptimeLimit);
            final String _tmpRateLimit;
            if (_cursor.isNull(_cursorIndexOfRateLimit)) {
              _tmpRateLimit = null;
            } else {
              _tmpRateLimit = _cursor.getString(_cursorIndexOfRateLimit);
            }
            _item.setRateLimit(_tmpRateLimit);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
