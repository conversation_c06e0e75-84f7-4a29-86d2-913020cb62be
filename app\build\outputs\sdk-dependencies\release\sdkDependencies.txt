# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "androidx.databinding"
    artifactId: "viewbinding"
    version: "8.10.1"
  }
  digests {
    sha256: "\207R&m\314\267\322D\026\021<@\n\327\370\035\00253\376\330\353\200\017\2700\341\001b7D\342"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.6.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-jvm"
    version: "1.6.0"
  }
  digests {
    sha256: "`\261\v^\365v\233yW\001r\340\025\270\025\224\005\311/\003K\250\213\223\221\251wX\234\235\353N"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "1.9.10"
  }
  digests {
    sha256: "U\351\211\305\022\270\t\ay\237\205C\t\363\274w\202\305\263\32192D-\003y\325\304rq\025\004"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-common"
    version: "1.9.10"
  }
  digests {
    sha256: "\315\3434\033\241\212+\242b\260\267\317lU\262\f\220\350\3244\344,\232\023\346\243\367p\333\226Z\210"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "23.0.0"
  }
  digests {
    sha256: "{\017\031r@\202\313\374\274f\345\253\352+\233\311,\360\212\036\241\036\031\0313\355C\200\036\263\315\005"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.databinding"
    artifactId: "databinding-common"
    version: "8.10.1"
  }
  digests {
    sha256: "f\312\270&9\332\300\366\302C4d\300\223\260t\326\b\304\273\210~\303\212\233\213\304\254\230\022g2"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.databinding"
    artifactId: "databinding-runtime"
    version: "8.10.1"
  }
  digests {
    sha256: "\303||J\263T3:\022\230\271+\3231\205+\230s{\347\004\372ey\021\314)\213u\264\261_"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.1.0"
  }
  digests {
    sha256: "c*\016T\aF\035\347t@\223R\224\016)*)\0207rB\a\247\207\202\fw\332\367\323;r"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.7.0"
  }
  digests {
    sha256: "\201\306\373\035\273j<\327\334\202}{\b\341\310\024.\323\244\000\243B$A\020\204\200\342/\2117\304"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.2.0"
  }
  digests {
    sha256: "e0\212\006\261\300\016\341\206\313\236\0312\023\203\360C\271\223\201?\025\"\304\177J>3\003\275\272A"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.2.0"
  }
  digests {
    sha256: "\241\276^\f\252+\ab8b\257j\342\033:\260q\201#$Q\204\320\343\r\352\201\265?\231\nG"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.7.0"
  }
  digests {
    sha256: "S=\355\216\340dZ\030\366e=\245?\341\354Z\025C\016\1776\2477\324^A\0051\363\0173\030"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-android"
    version: "1.7.1"
  }
  digests {
    sha256: "\020s\023v\f\030\370\332\027N\215\201\003PJF\216\200n\210\367\265Z\204\275\034\016\256\352\021\216\232"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core"
    version: "1.7.1"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core-jvm"
    version: "1.7.1"
  }
  digests {
    sha256: "t\226\317\375\323\353\020\020\232\315\332\0342\022\366\254x\025x\236\t8\r\311\342\314\336\304\226\333\243\374"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-bom"
    version: "1.7.1"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk8"
    version: "1.9.10"
  }
  digests {
    sha256: "\244\307M\224\326L\341\253\3457`\376\003\211\335\224\037o\305X\320\332\263^G\300\205\241\036\310\017("
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk7"
    version: "1.9.10"
  }
  digests {
    sha256: "\254ca\277\232\321\3558,!\003\331q,G\315\354\026b2\264\220>\325\226\350\207k\006\201\311\267"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata"
    version: "2.7.0"
  }
  digests {
    sha256: "\232\377\242La`\334\214\255\252\311B-OqNP\tS}\002C\257\304\274t\265\267\317\r\344\255"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.7.0"
  }
  digests {
    sha256: "\257\216\021\270~\003\rn1\a\3559\255\317\001\372\020\326%\236\261\\C\313\246\347\264\343\377\256\337\'"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core-ktx"
    version: "2.7.0"
  }
  digests {
    sha256: "\251\177L!\221\353\3352\371\232\302)Y{\321\251$F\260\034\321\240\210\3214\224\314\005\312\277\r\357"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-ktx"
    version: "2.7.0"
  }
  digests {
    sha256: "\222\375\017\002\273YO\a\264\017\202p\3533**\341\345\030\313\246\376\307\320b\363\366\004<\231\315\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.7.0"
  }
  digests {
    sha256: "N\035\222\342\211\222\f\327\265\0166q\271\031\033\324\023@sI\315\246\366\002\260(Td\364\034\034\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-process"
    version: "2.7.0"
  }
  digests {
    sha256: "o\263=\224s\244\223=\251\331\212\v\022\266\006\022~\200h\033\331\271\003\t\314\322\302#\bc\2719"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.startup"
    artifactId: "startup-runtime"
    version: "1.1.1"
  }
  digests {
    sha256: "\340\2462\2327\022b\376LE\003r\267\017\332\363;v\236\366\221p\224r7\207\317\316\211k\035\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.0.0"
  }
  digests {
    sha256: "\a\270\266\023\226e\270\204\241b\354\317\227\211\034\245\017\177V\203\0223\277%\026\212\340O{V\206\022"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx"
    version: "2.7.0"
  }
  digests {
    sha256: "\357p3\261]\262uiw\034<\n\353o\2229+VT!a\225\376t\023n\243\341\b\221*\324"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-ktx"
    version: "2.7.0"
  }
  digests {
    sha256: "\264\222\333\374\205\222dq6q\022\366\320\345\322\311\231\036\205\\T!\25379\223\226\314\001#\342\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate"
    version: "2.7.0"
  }
  digests {
    sha256: "W\305x\206.!\2366\fiW\377\v\312o\026\227\a\261\345X-O\245\223\342\204\367\366>3\366"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-ktx"
    version: "1.13.0"
  }
  digests {
    sha256: "Y\305Na)\351&\034m\201\017\216\352Jn\342\364\231M\201b\261\315\237\310\214\234\204\311*\314\374"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.13.0"
  }
  digests {
    sha256: "\033\226\310\353\020\304\264\002\203\375\326\351\252t\377\377\005\372\344\361]T\366\033\246\235Q\177\315\024F\225"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.4.0"
  }
  digests {
    sha256: "\306\353~g`\021\354e\263$(7=E\r\353\337\304Qy\304\370\263\247R\027O\270|\027\260\212"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures"
    version: "1.1.0"
  }
  digests {
    sha256: "\f\340g\305\024\240\321\004\235\033\353\337p\2364N\323&o\351tBuh)7\315\313\0233N\236"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "listenablefuture"
    version: "1.0"
  }
  digests {
    sha256: "\344\255v\a\345\300G|o\211\016\362jI\313\215\033\264\337\373e\v\253E\002\257\356ddN0i"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.interpolator"
    artifactId: "interpolator"
    version: "1.0.0"
  }
  digests {
    sha256: "3\03115\246O\342\037\242\303^\354f\210\361\247nQ&\006\300\374\203\334\033h\2367\255\327s*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.2.1"
  }
  digests {
    sha256: "!\247\324\274\366\275\271J\327\271(8\001R\223\000\264\373\270\200\214\244\361\221\340\315\316o\330\344pZ"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate-ktx"
    version: "1.2.1"
  }
  digests {
    sha256: "\205S\370~q6\302N\305$5`\364\217\0342\313\245m\252wr/\211X\232\\\257\313\217x\224"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.profileinstaller"
    artifactId: "profileinstaller"
    version: "1.3.1"
  }
  digests {
    sha256: "\320\344\002\3541\362@(\241\334~\266\240\243\371\331c\\\024Y9,\32749cC\267=g9H"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.databinding"
    artifactId: "databinding-adapters"
    version: "8.10.1"
  }
  digests {
    sha256: "\363\217*=L\372\327\220\325\360<g\271\'\315V\205\214\251\366\261\020^\263\256L/\305#\313C}"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat"
    version: "1.7.1"
  }
  digests {
    sha256: "*\3234\243#\262\200F\350\233s\214w\321\204\313=\314\243*U\032\260H\205\033/\332#\243\272&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.8.2"
  }
  digests {
    sha256: "\354\301\031&%0\246\342\371\363s ~G-Gc\006\276\323\324\262\026\376a\261\352B\343\276\366\210"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-ktx"
    version: "1.8.2"
  }
  digests {
    sha256: "\\x(=\031V\261K\"\317j\327\fq*s\252\bA\026\267SU&\027l;\205\346\251 Z"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat-resources"
    version: "1.7.1"
  }
  digests {
    sha256: "\216-\263\022$\312S\261\b\307\204\332+6\031Y\006\'\026\324\026\262\020\317\357=Z8(0m\360"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable"
    version: "1.1.0"
  }
  digests {
    sha256: "F\375c:\300\033I\267\374\253\302c\277\t\214Z\213\236\232iwM#N\334\312\004\373\002\337\216&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable-animated"
    version: "1.1.0"
  }
  digests {
    sha256: "v\332,P#q\331\303\200T\337^+$\215\000\332\207\200\236\320X\3636>\256\207\316^$\003\370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.cursoradapter"
    artifactId: "cursoradapter"
    version: "1.0.0"
  }
  digests {
    sha256: "\250\034\217\347\210\025\372G\337[t\235\353Rrz\321\037\223\227\332X\261`\027\364\353,\021\342\205d"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.drawerlayout"
    artifactId: "drawerlayout"
    version: "1.1.1"
  }
  digests {
    sha256: ",_\r\3127\216\267\214\242\304@?\230\211\307}\2520Y0\"`\362j\a\376\237c\300\211&\376"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview"
    version: "1.1.0"
  }
  digests {
    sha256: "\001\367j\260Cw\n\227\260T\004o\230\025q{\202\316\003U\300)g\321la\230\023Y\334\030\232"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2"
    version: "1.3.0"
  }
  digests {
    sha256: "+\3628\030\262:\231m\332\241\265\375[\263!)\332\377k\273-\316\025\026n/\314\335 \020\261\245"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2-views-helper"
    version: "1.3.0"
  }
  digests {
    sha256: "\232\023Q)ZOs\235\360\357\3504J\332\251\257\263HV\303\257XMJ\232\373\354\020ZE\271\v"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment"
    version: "1.6.2"
  }
  digests {
    sha256: "\337{\247?7}\275|\315(\261\022\257\320\215(\266\016\302\306\274\221\354e\206\t\310ED*\316\b"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.loader"
    artifactId: "loader"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\3675\313;U\304X\324p\276\331\342RT7[Q\213K\033\255i&x:p&\333\017P%"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager"
    artifactId: "viewpager"
    version: "1.0.0"
  }
  digests {
    sha256: "\024z\364\341J\031\204\001\r\217\025^^\031\327\201\360<\035p\337\355\002\250\340\321\204(\270\374\206\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment-ktx"
    version: "1.6.2"
  }
  digests {
    sha256: "F\f\2206\266M\247\316z\006j\f\261\204\340\024Q\317\304I)\252\033b\376\006\211Y\177#C\370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-ktx"
    version: "1.1.0"
  }
  digests {
    sha256: "+\374TG\\\004q1\2213a\365m\017\177\001\234n[\356S\356\260\353}\224\247\304\231\240R\'"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.resourceinspection"
    artifactId: "resourceinspection-annotation"
    version: "1.0.1"
  }
  digests {
    sha256: "\214\377\207\016\306\3731\333H\245/Jy#5\264\277\215\340~\003\2757\2021\201Rd3\314\325\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.material"
    artifactId: "material"
    version: "1.12.0"
  }
  digests {
    sha256: "Jg)A\266&\271\253\221\256\211>\322%\230\352S\255i\022\\\205\214\nY\372\233\220\332\245\313\b"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-bom"
    version: "1.8.22"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.errorprone"
    artifactId: "error_prone_annotations"
    version: "2.15.0"
  }
  digests {
    sha256: "\006pGqCI\347x\232[\333\372\331\321\300\257\237:\036\262\214U\240\356?h\346\202\371\005\304\353"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.cardview"
    artifactId: "cardview"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\223\300L\"\243\326\265\224m\256\237N\214Y\326\255\336jq\266\275]\207\373\231\330-\332\032\376\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.coordinatorlayout"
    artifactId: "coordinatorlayout"
    version: "1.1.0"
  }
  digests {
    sha256: "D\251\343\n\277V\257\020%\305*\n\365\006\376\351\304\023\032\245^\375\245/\237\331E\022\021\305\350\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout"
    version: "2.1.4"
  }
  digests {
    sha256: "\r\367\024\300\265\036Tq\016\277tn\264i\3233\027k\273<\262\237\200w]\303\312N\263\026%\022"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout-core"
    version: "1.0.4"
  }
  digests {
    sha256: ">G\177M\3421\345\213%\365\251\222\363\276E\351}3,4\243\232\236>}Kx\256\n\302%o"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.dynamicanimation"
    artifactId: "dynamicanimation"
    version: "1.0.0"
  }
  digests {
    sha256: "\316\000Qb\302)\2770\215-[\022\373l\255\bt\006\234\273\352\314\356c\250\031;\320\215@\336\004"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-utils"
    version: "1.0.0"
  }
  digests {
    sha256: "\247\355\317\001\325\265+04\a0\'\274Gu\267\212Gd\273b\002\273\221\326\034\202\232\335\215\321\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.documentfile"
    artifactId: "documentfile"
    version: "1.0.0"
  }
  digests {
    sha256: "\206Z\006\036\362\372\321e\"\370C56\270\324r\b\304o\367\307tQ\227\337\241\356\264\201\206\224\207"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.localbroadcastmanager"
    artifactId: "localbroadcastmanager"
    version: "1.0.0"
  }
  digests {
    sha256: "\347\0342\214\356\365\304\247\327o-\206\337\033e\326_\342\254\370h\261\244\357\330J?43a\206\330"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.print"
    artifactId: "print"
    version: "1.0.0"
  }
  digests {
    sha256: "\035\\\17715\241\273\246a\3747?\327.\021\353\nJ\333\2639g\207\202m\330\344\031\r]\236\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.recyclerview"
    artifactId: "recyclerview"
    version: "1.3.2"
  }
  digests {
    sha256: "\000\\\365\025\020I:$\372H\272\256\032EZRXQS\2215\032qq}\323<\272\225!\031f"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview-poolingcontainer"
    version: "1.0.0"
  }
  digests {
    sha256: "5\204\020/\304\233\363\231\305n;{\344\277\341 \000\304a\0222\f\330\317\205\314\n\217\223\363\347R"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager2"
    artifactId: "viewpager2"
    version: "1.1.0-beta02"
  }
  digests {
    sha256: "\272\372\303\312\231\036\326\212,|\246\3775)f\322\000\261.f\243B\321\017I|\270\026\217Y\005J"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.transition"
    artifactId: "transition"
    version: "1.5.0"
  }
  digests {
    sha256: "\n\246j\016\244\006\322Z\020\221\371j;u;K\022\344O\334C\271\036\305,\027\203\036\2341\365K"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-fragment-ktx"
    version: "2.7.6"
  }
  digests {
    sha256: "a\304\234\357\253\235\006J\352\030\231\354\242\300<\002hl\242,j\367\337D\355\177\005\255\306\271!\357"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-fragment"
    version: "2.7.6"
  }
  digests {
    sha256: "\372\'\3242\002z\3565Y\304\335{\020\365\226\"!\b\205\352\216\217\302\205\367I\033\356\342\262\024\262"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-runtime"
    version: "2.7.6"
  }
  digests {
    sha256: "\333\255-K\312\257\260\r\r\346\274o\315\n\030\207\025j{\257\343<\242\313\345\203A\355\300\324:\373"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-common"
    version: "2.7.6"
  }
  digests {
    sha256: "3\315U\3019Qr$\2376\255\231\037s\337x\213B\016\374\302\250/\022\310\244\027\365\317\364\b,"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-common-ktx"
    version: "2.7.6"
  }
  digests {
    sha256: "\311\325\267\331+ \030\252\351\205\027\266\003\244E\034\347\225\221B\\\020\227\376\024+\301,\352\vS\274"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-runtime-ktx"
    version: "2.7.6"
  }
  digests {
    sha256: "\231o\242es\367\205\025\250\322\365.\272\037\262G\031\a\bd\2471[\347\022b\032WDU\347V"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-ui"
    version: "2.7.6"
  }
  digests {
    sha256: "\230&n\026\312\214\343,\321\301,\225\031\207\177h\236\270\307(\\\241\334\211^\321\361e\227\334\177\355"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-ui-ktx"
    version: "2.7.6"
  }
  digests {
    sha256: "\032\352\232\203\022u\017\215G3\332\213\243z\2221:\351\262I%D\270#\354nV\034IJ\rg"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.slidingpanelayout"
    artifactId: "slidingpanelayout"
    version: "1.2.0"
  }
  digests {
    sha256: "_S3\233\342\244\371\n\232\276\243W\035\325\236p\250\244\236\177\025\335\202\227J8\230\264e.\207\024"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.window"
    artifactId: "window"
    version: "1.0.0"
  }
  digests {
    sha256: "2\022\230[\344\022ss\312M\016\247\370\270\032%\n\342\020^\222Oy@\020]\006z\017\232\3010"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-runtime"
    version: "2.6.1"
  }
  digests {
    sha256: "ibO\327\255\326\316[\374\301#b\315BsA\322\221\016\'~\325\246\374\304a2\244\211\221\024\320"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-common"
    version: "2.6.1"
  }
  digests {
    sha256: "\2312m>\354\244\246Fq\274\210\002\361\344_Nk4\216\214\177\253\2420\303\205M1h\t\377\317"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-ktx"
    version: "2.6.1"
  }
  digests {
    sha256: "\332L\f\272~\374\257\242\237\276\253\035\264\031\204#\217%\301\2436\022\301\326\rc\271\225\226\215p\312"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite"
    version: "2.4.0"
  }
  digests {
    sha256: "\273\177\241\023\021/~HWIn\".0Q\327:\221\n\335t\277@v\036\033\332\345[\002\026\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite-framework"
    version: "2.4.0"
  }
  digests {
    sha256: "\312nP3\"\262\346\003t\302\263l\225\305\v\026p\235\223\210\3766\350\017\262=\341\373\367\246\353\225"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "okhttp"
    version: "4.12.0"
  }
  digests {
    sha256: "\261\005\000\201\261K\267\243\247\345ZM>\360\033]\317\253\304S\264W:O\300\031vq\221\325\364\340"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio"
    version: "3.6.0"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio-jvm"
    version: "3.6.0"
  }
  digests {
    sha256: "gT?\a6\374B*\351\'\355\016PK\230\274^&\237\332\r5\000W\2237\313q=\242\204\022"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.retrofit2"
    artifactId: "retrofit"
    version: "2.9.0"
  }
  digests {
    sha256: "\346\352\031)\304hR\365\276\306j\2635}\243\203Gl\357N\215\035\356\375\277\031[y\314Me\201"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.retrofit2"
    artifactId: "converter-gson"
    version: "2.9.0"
  }
  digests {
    sha256: "2\252 k\232)\311\337^\332\223\240\222\317\263\260\271\023>#,\006+\252\210/\003\031\360\347\237\016"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.code.gson"
    artifactId: "gson"
    version: "2.10.1"
  }
  digests {
    sha256: "BA\301Jw\'\303O\356\246P~\310\0011\212=J\220\360p\344RV\201\a\237\271N\344\305\223"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "logging-interceptor"
    version: "4.12.0"
  }
  digests {
    sha256: "\363\350\325\360\220<%\f+U\322\364\177\317\340\b\350\00648]\2508Qa\307\246:\256\320\307L"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.itextpdf"
    artifactId: "itext7-core"
    version: "8.0.2"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.itextpdf"
    artifactId: "itext-core"
    version: "8.0.2"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.itextpdf"
    artifactId: "barcodes"
    version: "8.0.2"
  }
  digests {
    sha256: "\355\331\367\213\005nZ\033\037Q\360\317kKt\3612\v\252Xf\370\352\204\363\207\334x\353Y\214<"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.itextpdf"
    artifactId: "kernel"
    version: "8.0.2"
  }
  digests {
    sha256: ".,)k\r$\244X\351\002\374\214\n\266\315T\t\325B\332\221\361\200\265z\307\017V\233\002\313\330"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.itextpdf"
    artifactId: "bouncy-castle-connector"
    version: "8.0.2"
  }
  digests {
    sha256: "\300?\2765\211\333%\bl\204\210\177\313p\022\241\267\363\'\n\0368\373\327\203\313M\204\352\357K\200"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.slf4j"
    artifactId: "slf4j-api"
    version: "1.7.36"
  }
  digests {
    sha256: "\323\357W^>Iyg\215\300\033\361\334\316Q\002\024\223\264\321\037\267\361\276\212\331\202\207|\026\241\300"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.itextpdf"
    artifactId: "io"
    version: "8.0.2"
  }
  digests {
    sha256: "\371\254\"\243\203!o\230P\225\314\232\330\240\020T\300Y.\032p\260f\3765/\001\3048\234\206."
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.itextpdf"
    artifactId: "commons"
    version: "8.0.2"
  }
  digests {
    sha256: "Q\t\300\335\"\224B\345\220\324\234Gf\2241+\313j\264a\253F\352\372\331)o\302[\350\233\347"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.itextpdf"
    artifactId: "font-asian"
    version: "8.0.2"
  }
  digests {
    sha256: "M:\314\273\033V>\256\327\332!\257Y\025W]\022\217\245\017\333\310[\226PR\253\332\305\024\264\211"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.itextpdf"
    artifactId: "forms"
    version: "8.0.2"
  }
  digests {
    sha256: "\224\at\201A\216\273\023\351\315\320\003\356\346\f\t!\235\337\a{\023\246\221\373\005\245\027M\\.\324"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.itextpdf"
    artifactId: "layout"
    version: "8.0.2"
  }
  digests {
    sha256: "\222\373\313\357U\330\240\037\214\355q\263w\277\272\031\331\016\220{\tC\301\266Q\fms\'\314\331+"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.itextpdf"
    artifactId: "hyph"
    version: "8.0.2"
  }
  digests {
    sha256: ">R\215\316\276\340\235\032\036\177\251\210f\233\232d\250\277\342\277\021A\314_\230\372\313UY\003\373\304"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.itextpdf"
    artifactId: "pdfa"
    version: "8.0.2"
  }
  digests {
    sha256: "\202\343*\352\343\001\354\036d6\303\210\321\300\273G1KRZ+\336m\257\235\221E\303\312t\362\203"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.itextpdf"
    artifactId: "sign"
    version: "8.0.2"
  }
  digests {
    sha256: "Q\330\343\227\207c\270;Je\350R\021{>1Tg\230\264\236\216]\203\312/\353D\347\377\\@"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.itextpdf"
    artifactId: "styled-xml-parser"
    version: "8.0.2"
  }
  digests {
    sha256: "\275\341\306\al\275\r\302\372)\337\302\032\031\277\210s;\2424\204hx=\b\362K\370\347\022gI"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.itextpdf"
    artifactId: "svg"
    version: "8.0.2"
  }
  digests {
    sha256: "\341\332V\376\005?3@\341\374\272\344l\247{O\r\313\275\350\231R\355\"o\305\016\232\236\213I\331"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.journeyapps"
    artifactId: "zxing-android-embedded"
    version: "4.3.0"
  }
  digests {
    sha256: "J\2603S\022|4\345\\\273\200\373\311\243@1\336\313\317\261\354^\217\231\031D\322\324\224b\0323"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.zxing"
    artifactId: "core"
    version: "3.5.2"
  }
  digests {
    sha256: "\373\251\001\"\265\325k\254\214\224|`\265\222R\021\260\330\324\fS\2331\t\2767t\360\217t\376\333"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "glide"
    version: "4.16.0"
  }
  digests {
    sha256: "\211\201\034c\335&jHQ\375\033y\306\374\f\230)\226\354\3445\365\377H?\2700\312l\265<\324"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "gifdecoder"
    version: "4.16.0"
  }
  digests {
    sha256: "\225_\207*\364\322\243!\376\242\243F\374G\"%\242\271\225$\254\304g\201I\027\022\343\233j!O"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "disklrucache"
    version: "4.16.0"
  }
  digests {
    sha256: "\242\'\366U\234\020J\245\245\310\213\np\351\307\343\333hY\343\vGt\363\202\221!\337\236bst"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "annotations"
    version: "4.16.0"
  }
  digests {
    sha256: "\305\2369\261\263\033MU\222\'|\331\000\022\024\235t\vV\235C8\365dC\312\310,?\275\243\321"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.exifinterface"
    artifactId: "exifinterface"
    version: "1.3.6"
  }
  digests {
    sha256: "\030\004\020^\236\005\375\330\367`A;\255]\344\230\303\201\2522\237O\235\224\310Q\274\211\032\306T\306"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.karumi"
    artifactId: "dexter"
    version: "6.2.3"
  }
  digests {
    sha256: "Jr\353\210\306F\037,d\'k\217]\316\006\314\347]\265\026\310\006\004D(v\250\025\373Cx\215"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.security"
    artifactId: "security-crypto"
    version: "1.1.0-alpha06"
  }
  digests {
    sha256: "\227a\021\027\v?\023\322\360\371E}\237\237W\223G\024<\266\\\005Be\220m3\307a\212L*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.crypto.tink"
    artifactId: "tink-android"
    version: "1.8.0"
  }
  digests {
    sha256: "^\376\217\034\266\347X\024\256\006y=_\370\331\260\036o\210\357\227\354\300b\321N\274\'\002}\277x"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.opencsv"
    artifactId: "opencsv"
    version: "5.8"
  }
  digests {
    sha256: "\215-_\270\367\036\226\374\342\260\323q\a\276\350\374\211H\266\247k~\306+R1[\3738(\365\264"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.apache.commons"
    artifactId: "commons-lang3"
    version: "3.12.0"
  }
  digests {
    sha256: "\331\031\331\004Hl\003\177\215\0314\022\332\f\222\342*\237\242B0\271\326zW\205\\\\1\307\351N"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.apache.commons"
    artifactId: "commons-text"
    version: "1.10.0"
  }
  digests {
    sha256: "w\f\331\003\372{`M\037~\367\272\027\370A\bfr\224\262\264x\276\216\321\257;\377\264\256\000\030"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "commons-beanutils"
    artifactId: "commons-beanutils"
    version: "1.9.4"
  }
  digests {
    sha256: "}\223\214\201x\220(\004\\\b\300e\351K\347_\302\200Rv \325\275b\265\031\325\203\20526\212"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "commons-logging"
    artifactId: "commons-logging"
    version: "1.2"
  }
  digests {
    sha256: "\332\335\352\036\240\276\017V\227\212\263\000k\212\311(4\257\356\373\331\267\344\3461o\312W\337\017\2466"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "commons-collections"
    artifactId: "commons-collections"
    version: "3.2.2"
  }
  digests {
    sha256: "\356\352\351\027\221qD\246\212t\035L\r\377f\252\\\\_\330U\223\377!{\316\323\374\214\247\203\270"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.apache.commons"
    artifactId: "commons-collections4"
    version: "4.4"
  }
  digests {
    sha256: "\035\370\271C\v\\\216\321C\327\201^@>3\357Sq\262@\n\255\276\233\332\b\203v.\bF\321"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.apache.poi"
    artifactId: "poi"
    version: "5.2.4"
  }
  digests {
    sha256: "\242+&(L\341\025\027L[\324\211\005\241\f\367Q*\037\211\374c\332\207r\364VJ\206\246\273\244"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "commons-codec"
    artifactId: "commons-codec"
    version: "1.16.0"
  }
  digests {
    sha256: "VY_\262\v\v\205\274\221\320\325\003\332\325\v\267\361\271\257\300\356\325\337\372l\273%\222\220\000HM"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.apache.commons"
    artifactId: "commons-math3"
    version: "3.6.1"
  }
  digests {
    sha256: "\036V\327\260X\322\213e\253\322V\270E\2168\205\266t\301\325\210\372C\315}\034\273\234~\362\263\b"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "commons-io"
    artifactId: "commons-io"
    version: "2.13.0"
  }
  digests {
    sha256: "g\036\2529h\215\254/\372\244d[<\231\200\256-\016\242G\036J\346\245\332\031\234\321Z\3426f"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.zaxxer"
    artifactId: "SparseBitSet"
    version: "1.3"
  }
  digests {
    sha256: "\367k\205\255\260\300\a!\256&{|\375\344\332\177q\323\022\034\302\026\f\237\300\f\f\211\370\305<\212"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.apache.logging.log4j"
    artifactId: "log4j-api"
    version: "2.20.0"
  }
  digests {
    sha256: "/C\356\246y\352f\361L\240\361?\354*\206\000\254\022OZR1\334\264\337\203\223\355\334\271uP"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.apache.poi"
    artifactId: "poi-ooxml"
    version: "5.2.4"
  }
  digests {
    sha256: "\360\375Ro\351\243b\231\210Eko\374]*\021\274{g\2349\313\201\241{\337\371\"\267\224\255Q"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.apache.poi"
    artifactId: "poi-ooxml-lite"
    version: "5.2.4"
  }
  digests {
    sha256: "O\302\317-%6\261\255\200.\027g\030zM\205\024b\2455\245\177\027\343\365B\301\211\312\254[\322"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.apache.xmlbeans"
    artifactId: "xmlbeans"
    version: "5.1.1"
  }
  digests {
    sha256: "_HJx\276\327\034\277\3767\tg\213k\3354cx\032|a\306\331\207#0\256\313\361Pv*"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.apache.commons"
    artifactId: "commons-compress"
    version: "1.24.0"
  }
  digests {
    sha256: "\373\362\305\322u\345\023\300\217NO\2659rm\277:\302\024*\371V\272\223\357\271\203$O\f6\275"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.virtuald"
    artifactId: "curvesapi"
    version: "1.08"
  }
  digests {
    sha256: "\255\225\260\213\213\277\235}\027\345\340\b\024\211\217\2423$\363+\305\266/\0327\200\036jV\316\000y"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.jakewharton.timber"
    artifactId: "timber"
    version: "5.0.1"
  }
  digests {
    sha256: "\306\355\335\374\310\357\364*\026\004\310W\177\317\244\264\377\331\362R\022,R\3526\317\347\226\177Q/q"
  }
  repo_index {
    value: 1
  }
}
library_dependencies {
  library_dep_index: 1
}
library_dependencies {
  library_index: 1
  library_dep_index: 2
}
library_dependencies {
  library_index: 2
  library_dep_index: 3
}
library_dependencies {
  library_index: 3
  library_dep_index: 4
  library_dep_index: 5
}
library_dependencies {
  library_index: 7
  library_dep_index: 8
  library_dep_index: 6
  library_dep_index: 0
  library_dep_index: 9
}
library_dependencies {
  library_index: 8
  library_dep_index: 1
}
library_dependencies {
  library_index: 9
  library_dep_index: 1
  library_dep_index: 10
  library_dep_index: 11
  library_dep_index: 12
  library_dep_index: 39
  library_dep_index: 3
  library_dep_index: 12
  library_dep_index: 19
  library_dep_index: 21
  library_dep_index: 22
  library_dep_index: 24
  library_dep_index: 27
  library_dep_index: 23
  library_dep_index: 28
  library_dep_index: 20
  library_dep_index: 29
}
library_dependencies {
  library_index: 10
  library_dep_index: 1
}
library_dependencies {
  library_index: 11
  library_dep_index: 1
  library_dep_index: 10
}
library_dependencies {
  library_index: 12
  library_dep_index: 1
  library_dep_index: 3
  library_dep_index: 13
  library_dep_index: 14
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 21
  library_dep_index: 22
  library_dep_index: 24
  library_dep_index: 9
  library_dep_index: 27
  library_dep_index: 23
  library_dep_index: 28
  library_dep_index: 29
}
library_dependencies {
  library_index: 13
  library_dep_index: 14
  library_dep_index: 16
  library_dep_index: 17
}
library_dependencies {
  library_index: 14
  library_dep_index: 15
}
library_dependencies {
  library_index: 15
  library_dep_index: 5
  library_dep_index: 16
  library_dep_index: 4
  library_dep_index: 17
}
library_dependencies {
  library_index: 16
  library_dep_index: 13
  library_dep_index: 15
  library_dep_index: 14
}
library_dependencies {
  library_index: 17
  library_dep_index: 3
  library_dep_index: 18
}
library_dependencies {
  library_index: 18
  library_dep_index: 3
}
library_dependencies {
  library_index: 19
  library_dep_index: 10
  library_dep_index: 11
  library_dep_index: 20
  library_dep_index: 21
  library_dep_index: 3
  library_dep_index: 14
  library_dep_index: 12
  library_dep_index: 20
  library_dep_index: 21
  library_dep_index: 22
  library_dep_index: 24
  library_dep_index: 9
  library_dep_index: 27
  library_dep_index: 23
  library_dep_index: 28
  library_dep_index: 29
}
library_dependencies {
  library_index: 20
  library_dep_index: 10
  library_dep_index: 11
  library_dep_index: 12
  library_dep_index: 3
  library_dep_index: 12
  library_dep_index: 19
  library_dep_index: 21
  library_dep_index: 22
  library_dep_index: 24
  library_dep_index: 9
  library_dep_index: 27
  library_dep_index: 23
  library_dep_index: 28
  library_dep_index: 29
}
library_dependencies {
  library_index: 21
  library_dep_index: 20
  library_dep_index: 3
  library_dep_index: 12
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 22
  library_dep_index: 24
  library_dep_index: 9
  library_dep_index: 27
  library_dep_index: 23
  library_dep_index: 28
  library_dep_index: 29
}
library_dependencies {
  library_index: 22
  library_dep_index: 19
  library_dep_index: 21
  library_dep_index: 3
  library_dep_index: 14
  library_dep_index: 19
  library_dep_index: 21
  library_dep_index: 9
  library_dep_index: 23
  library_dep_index: 28
  library_dep_index: 27
  library_dep_index: 24
  library_dep_index: 12
  library_dep_index: 20
  library_dep_index: 29
}
library_dependencies {
  library_index: 23
  library_dep_index: 1
  library_dep_index: 3
  library_dep_index: 12
  library_dep_index: 19
  library_dep_index: 21
  library_dep_index: 22
  library_dep_index: 24
  library_dep_index: 9
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 20
  library_dep_index: 29
}
library_dependencies {
  library_index: 24
  library_dep_index: 1
  library_dep_index: 9
  library_dep_index: 25
  library_dep_index: 3
  library_dep_index: 12
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 21
  library_dep_index: 22
  library_dep_index: 9
  library_dep_index: 27
  library_dep_index: 23
  library_dep_index: 28
  library_dep_index: 29
}
library_dependencies {
  library_index: 25
  library_dep_index: 1
  library_dep_index: 26
}
library_dependencies {
  library_index: 26
  library_dep_index: 1
}
library_dependencies {
  library_index: 27
  library_dep_index: 1
  library_dep_index: 9
  library_dep_index: 3
  library_dep_index: 13
  library_dep_index: 12
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 21
  library_dep_index: 22
  library_dep_index: 24
  library_dep_index: 9
  library_dep_index: 23
  library_dep_index: 28
  library_dep_index: 29
}
library_dependencies {
  library_index: 28
  library_dep_index: 23
  library_dep_index: 3
  library_dep_index: 13
  library_dep_index: 22
  library_dep_index: 9
  library_dep_index: 23
  library_dep_index: 19
  library_dep_index: 21
  library_dep_index: 27
  library_dep_index: 24
  library_dep_index: 12
  library_dep_index: 20
  library_dep_index: 29
}
library_dependencies {
  library_index: 29
  library_dep_index: 1
  library_dep_index: 30
  library_dep_index: 20
  library_dep_index: 23
  library_dep_index: 37
  library_dep_index: 3
  library_dep_index: 13
  library_dep_index: 12
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 21
  library_dep_index: 22
  library_dep_index: 24
  library_dep_index: 9
  library_dep_index: 27
  library_dep_index: 23
  library_dep_index: 28
}
library_dependencies {
  library_index: 30
  library_dep_index: 1
  library_dep_index: 31
  library_dep_index: 3
  library_dep_index: 31
}
library_dependencies {
  library_index: 31
  library_dep_index: 1
  library_dep_index: 32
  library_dep_index: 8
  library_dep_index: 33
  library_dep_index: 35
  library_dep_index: 9
  library_dep_index: 36
  library_dep_index: 3
  library_dep_index: 30
}
library_dependencies {
  library_index: 32
  library_dep_index: 3
}
library_dependencies {
  library_index: 33
  library_dep_index: 1
  library_dep_index: 34
}
library_dependencies {
  library_index: 35
  library_dep_index: 1
}
library_dependencies {
  library_index: 36
  library_dep_index: 1
  library_dep_index: 8
}
library_dependencies {
  library_index: 37
  library_dep_index: 1
  library_dep_index: 10
  library_dep_index: 12
  library_dep_index: 3
  library_dep_index: 38
}
library_dependencies {
  library_index: 38
  library_dep_index: 37
  library_dep_index: 3
  library_dep_index: 37
}
library_dependencies {
  library_index: 39
  library_dep_index: 1
  library_dep_index: 33
  library_dep_index: 25
  library_dep_index: 34
}
library_dependencies {
  library_index: 40
  library_dep_index: 7
  library_dep_index: 6
}
library_dependencies {
  library_index: 41
  library_dep_index: 42
  library_dep_index: 1
  library_dep_index: 44
  library_dep_index: 8
  library_dep_index: 31
  library_dep_index: 30
  library_dep_index: 47
  library_dep_index: 48
  library_dep_index: 50
  library_dep_index: 51
  library_dep_index: 52
  library_dep_index: 9
  library_dep_index: 23
  library_dep_index: 39
  library_dep_index: 57
  library_dep_index: 37
  library_dep_index: 3
  library_dep_index: 44
}
library_dependencies {
  library_index: 42
  library_dep_index: 1
  library_dep_index: 8
  library_dep_index: 31
  library_dep_index: 9
  library_dep_index: 23
  library_dep_index: 29
  library_dep_index: 39
  library_dep_index: 37
  library_dep_index: 26
  library_dep_index: 3
  library_dep_index: 43
}
library_dependencies {
  library_index: 43
  library_dep_index: 42
  library_dep_index: 30
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 38
  library_dep_index: 3
  library_dep_index: 42
}
library_dependencies {
  library_index: 44
  library_dep_index: 1
  library_dep_index: 8
  library_dep_index: 31
  library_dep_index: 45
  library_dep_index: 46
  library_dep_index: 41
}
library_dependencies {
  library_index: 45
  library_dep_index: 1
  library_dep_index: 31
  library_dep_index: 8
}
library_dependencies {
  library_index: 46
  library_dep_index: 45
  library_dep_index: 35
  library_dep_index: 8
}
library_dependencies {
  library_index: 47
  library_dep_index: 1
}
library_dependencies {
  library_index: 48
  library_dep_index: 1
  library_dep_index: 31
  library_dep_index: 49
}
library_dependencies {
  library_index: 49
  library_dep_index: 1
  library_dep_index: 31
  library_dep_index: 8
}
library_dependencies {
  library_index: 50
  library_dep_index: 1
  library_dep_index: 8
  library_dep_index: 31
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 51
}
library_dependencies {
  library_index: 51
  library_dep_index: 8
  library_dep_index: 31
  library_dep_index: 50
  library_dep_index: 50
}
library_dependencies {
  library_index: 52
  library_dep_index: 42
  library_dep_index: 1
  library_dep_index: 32
  library_dep_index: 8
  library_dep_index: 30
  library_dep_index: 20
  library_dep_index: 9
  library_dep_index: 23
  library_dep_index: 29
  library_dep_index: 53
  library_dep_index: 39
  library_dep_index: 37
  library_dep_index: 54
  library_dep_index: 3
  library_dep_index: 55
}
library_dependencies {
  library_index: 53
  library_dep_index: 1
  library_dep_index: 31
  library_dep_index: 19
  library_dep_index: 23
}
library_dependencies {
  library_index: 54
  library_dep_index: 1
  library_dep_index: 31
  library_dep_index: 49
}
library_dependencies {
  library_index: 55
  library_dep_index: 43
  library_dep_index: 56
  library_dep_index: 30
  library_dep_index: 52
  library_dep_index: 21
  library_dep_index: 28
  library_dep_index: 38
  library_dep_index: 3
  library_dep_index: 52
}
library_dependencies {
  library_index: 56
  library_dep_index: 3
  library_dep_index: 8
}
library_dependencies {
  library_index: 57
  library_dep_index: 1
}
library_dependencies {
  library_index: 58
  library_dep_index: 59
  library_dep_index: 60
  library_dep_index: 42
  library_dep_index: 1
  library_dep_index: 41
  library_dep_index: 61
  library_dep_index: 62
  library_dep_index: 63
  library_dep_index: 31
  library_dep_index: 48
  library_dep_index: 65
  library_dep_index: 32
  library_dep_index: 52
  library_dep_index: 9
  library_dep_index: 70
  library_dep_index: 57
  library_dep_index: 73
  library_dep_index: 45
  library_dep_index: 72
}
library_dependencies {
  library_index: 59
  library_dep_index: 3
  library_dep_index: 17
  library_dep_index: 4
  library_dep_index: 18
}
library_dependencies {
  library_index: 61
  library_dep_index: 1
}
library_dependencies {
  library_index: 62
  library_dep_index: 1
  library_dep_index: 31
  library_dep_index: 49
  library_dep_index: 8
}
library_dependencies {
  library_index: 63
  library_dep_index: 41
  library_dep_index: 31
  library_dep_index: 64
}
library_dependencies {
  library_index: 65
  library_dep_index: 31
  library_dep_index: 8
  library_dep_index: 66
}
library_dependencies {
  library_index: 66
  library_dep_index: 1
  library_dep_index: 31
  library_dep_index: 67
  library_dep_index: 53
  library_dep_index: 68
  library_dep_index: 69
}
library_dependencies {
  library_index: 67
  library_dep_index: 1
}
library_dependencies {
  library_index: 68
  library_dep_index: 1
}
library_dependencies {
  library_index: 69
  library_dep_index: 1
}
library_dependencies {
  library_index: 70
  library_dep_index: 1
  library_dep_index: 8
  library_dep_index: 31
  library_dep_index: 49
  library_dep_index: 71
  library_dep_index: 72
}
library_dependencies {
  library_index: 71
  library_dep_index: 30
  library_dep_index: 3
}
library_dependencies {
  library_index: 72
  library_dep_index: 1
  library_dep_index: 32
  library_dep_index: 8
  library_dep_index: 31
  library_dep_index: 52
  library_dep_index: 70
}
library_dependencies {
  library_index: 73
  library_dep_index: 1
  library_dep_index: 8
  library_dep_index: 31
  library_dep_index: 65
}
library_dependencies {
  library_index: 74
  library_dep_index: 75
  library_dep_index: 79
  library_dep_index: 75
  library_dep_index: 79
  library_dep_index: 81
  library_dep_index: 80
  library_dep_index: 76
  library_dep_index: 78
  library_dep_index: 77
}
library_dependencies {
  library_index: 75
  library_dep_index: 55
  library_dep_index: 76
  library_dep_index: 82
  library_dep_index: 3
  library_dep_index: 74
  library_dep_index: 76
  library_dep_index: 79
  library_dep_index: 80
  library_dep_index: 81
  library_dep_index: 78
  library_dep_index: 77
}
library_dependencies {
  library_index: 76
  library_dep_index: 43
  library_dep_index: 32
  library_dep_index: 8
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 77
  library_dep_index: 3
  library_dep_index: 77
  library_dep_index: 78
  library_dep_index: 75
  library_dep_index: 74
  library_dep_index: 79
  library_dep_index: 80
  library_dep_index: 81
}
library_dependencies {
  library_index: 77
  library_dep_index: 1
  library_dep_index: 56
  library_dep_index: 30
  library_dep_index: 12
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 39
  library_dep_index: 38
  library_dep_index: 3
  library_dep_index: 78
  library_dep_index: 75
  library_dep_index: 74
  library_dep_index: 76
  library_dep_index: 79
  library_dep_index: 80
  library_dep_index: 81
}
library_dependencies {
  library_index: 78
  library_dep_index: 77
  library_dep_index: 77
  library_dep_index: 75
  library_dep_index: 74
  library_dep_index: 76
  library_dep_index: 79
  library_dep_index: 80
  library_dep_index: 81
}
library_dependencies {
  library_index: 79
  library_dep_index: 78
  library_dep_index: 76
  library_dep_index: 78
  library_dep_index: 75
  library_dep_index: 74
  library_dep_index: 76
  library_dep_index: 80
  library_dep_index: 81
  library_dep_index: 77
}
library_dependencies {
  library_index: 80
  library_dep_index: 32
  library_dep_index: 49
  library_dep_index: 48
  library_dep_index: 76
  library_dep_index: 73
  library_dep_index: 58
  library_dep_index: 78
  library_dep_index: 75
  library_dep_index: 74
  library_dep_index: 76
  library_dep_index: 79
  library_dep_index: 81
  library_dep_index: 77
}
library_dependencies {
  library_index: 81
  library_dep_index: 79
  library_dep_index: 80
  library_dep_index: 75
  library_dep_index: 74
  library_dep_index: 79
  library_dep_index: 80
  library_dep_index: 76
  library_dep_index: 78
  library_dep_index: 77
}
library_dependencies {
  library_index: 82
  library_dep_index: 1
  library_dep_index: 49
  library_dep_index: 31
  library_dep_index: 83
  library_dep_index: 73
}
library_dependencies {
  library_index: 83
  library_dep_index: 3
  library_dep_index: 13
  library_dep_index: 1
  library_dep_index: 8
  library_dep_index: 31
}
library_dependencies {
  library_index: 84
  library_dep_index: 32
  library_dep_index: 11
  library_dep_index: 85
  library_dep_index: 87
  library_dep_index: 88
  library_dep_index: 85
  library_dep_index: 86
}
library_dependencies {
  library_index: 85
  library_dep_index: 1
  library_dep_index: 17
  library_dep_index: 86
  library_dep_index: 84
}
library_dependencies {
  library_index: 86
  library_dep_index: 85
  library_dep_index: 84
  library_dep_index: 3
  library_dep_index: 13
  library_dep_index: 85
  library_dep_index: 84
}
library_dependencies {
  library_index: 87
  library_dep_index: 1
  library_dep_index: 3
  library_dep_index: 88
}
library_dependencies {
  library_index: 88
  library_dep_index: 1
  library_dep_index: 87
  library_dep_index: 3
  library_dep_index: 87
}
library_dependencies {
  library_index: 89
  library_dep_index: 90
  library_dep_index: 17
}
library_dependencies {
  library_index: 90
  library_dep_index: 91
}
library_dependencies {
  library_index: 91
  library_dep_index: 17
  library_dep_index: 4
}
library_dependencies {
  library_index: 92
  library_dep_index: 89
}
library_dependencies {
  library_index: 93
  library_dep_index: 92
  library_dep_index: 94
}
library_dependencies {
  library_index: 95
  library_dep_index: 89
  library_dep_index: 17
}
library_dependencies {
  library_index: 96
  library_dep_index: 97
}
library_dependencies {
  library_index: 97
  library_dep_index: 98
  library_dep_index: 104
  library_dep_index: 105
  library_dep_index: 107
  library_dep_index: 102
  library_dep_index: 99
  library_dep_index: 106
  library_dep_index: 108
  library_dep_index: 109
  library_dep_index: 110
  library_dep_index: 111
  library_dep_index: 100
}
library_dependencies {
  library_index: 98
  library_dep_index: 99
  library_dep_index: 101
}
library_dependencies {
  library_index: 99
  library_dep_index: 100
  library_dep_index: 102
  library_dep_index: 101
}
library_dependencies {
  library_index: 100
  library_dep_index: 101
}
library_dependencies {
  library_index: 102
  library_dep_index: 103
  library_dep_index: 101
}
library_dependencies {
  library_index: 103
  library_dep_index: 101
}
library_dependencies {
  library_index: 104
  library_dep_index: 101
}
library_dependencies {
  library_index: 105
  library_dep_index: 99
  library_dep_index: 106
  library_dep_index: 101
}
library_dependencies {
  library_index: 106
  library_dep_index: 99
  library_dep_index: 101
}
library_dependencies {
  library_index: 107
  library_dep_index: 101
}
library_dependencies {
  library_index: 108
  library_dep_index: 105
  library_dep_index: 99
  library_dep_index: 101
}
library_dependencies {
  library_index: 109
  library_dep_index: 100
  library_dep_index: 105
  library_dep_index: 99
  library_dep_index: 106
  library_dep_index: 108
  library_dep_index: 101
}
library_dependencies {
  library_index: 110
  library_dep_index: 102
  library_dep_index: 99
  library_dep_index: 106
  library_dep_index: 101
}
library_dependencies {
  library_index: 111
  library_dep_index: 106
  library_dep_index: 110
  library_dep_index: 101
}
library_dependencies {
  library_index: 112
  library_dep_index: 113
}
library_dependencies {
  library_index: 114
  library_dep_index: 115
  library_dep_index: 116
  library_dep_index: 117
  library_dep_index: 52
  library_dep_index: 46
  library_dep_index: 118
  library_dep_index: 26
}
library_dependencies {
  library_index: 115
  library_dep_index: 1
}
library_dependencies {
  library_index: 118
  library_dep_index: 1
}
library_dependencies {
  library_index: 119
  library_dep_index: 41
  library_dep_index: 58
}
library_dependencies {
  library_index: 120
  library_dep_index: 1
  library_dep_index: 1
  library_dep_index: 8
  library_dep_index: 121
}
library_dependencies {
  library_index: 121
  library_dep_index: 94
}
library_dependencies {
  library_index: 122
  library_dep_index: 123
  library_dep_index: 124
  library_dep_index: 125
  library_dep_index: 128
}
library_dependencies {
  library_index: 124
  library_dep_index: 123
}
library_dependencies {
  library_index: 125
  library_dep_index: 126
  library_dep_index: 127
}
library_dependencies {
  library_index: 129
  library_dep_index: 130
  library_dep_index: 128
  library_dep_index: 131
  library_dep_index: 132
  library_dep_index: 133
  library_dep_index: 134
}
library_dependencies {
  library_index: 135
  library_dep_index: 129
  library_dep_index: 136
  library_dep_index: 137
  library_dep_index: 138
  library_dep_index: 132
  library_dep_index: 139
  library_dep_index: 134
  library_dep_index: 128
}
library_dependencies {
  library_index: 136
  library_dep_index: 137
}
library_dependencies {
  library_index: 137
  library_dep_index: 134
}
library_dependencies {
  library_index: 140
  library_dep_index: 3
  library_dep_index: 5
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 6
  dependency_index: 7
  dependency_index: 40
  dependency_index: 41
  dependency_index: 58
  dependency_index: 30
  dependency_index: 63
  dependency_index: 28
  dependency_index: 22
  dependency_index: 55
  dependency_index: 43
  dependency_index: 74
  dependency_index: 81
  dependency_index: 70
  dependency_index: 61
  dependency_index: 84
  dependency_index: 86
  dependency_index: 89
  dependency_index: 92
  dependency_index: 93
  dependency_index: 95
  dependency_index: 94
  dependency_index: 96
  dependency_index: 106
  dependency_index: 99
  dependency_index: 102
  dependency_index: 112
  dependency_index: 113
  dependency_index: 114
  dependency_index: 119
  dependency_index: 120
  dependency_index: 122
  dependency_index: 129
  dependency_index: 135
  dependency_index: 140
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://jitpack.io"
  }
}
