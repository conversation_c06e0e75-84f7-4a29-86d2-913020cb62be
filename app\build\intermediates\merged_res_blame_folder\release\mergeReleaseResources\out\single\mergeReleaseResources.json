[{"merged": "com.mikrotik.cardgenerator.app-release-59:/drawable_ic_mikrotik_logo.xml.flat", "source": "com.mikrotik.cardgenerator.app-main-60:/drawable/ic_mikrotik_logo.xml"}, {"merged": "com.mikrotik.cardgenerator.app-release-59:/mipmap-xhdpi_ic_launcher.webp.flat", "source": "com.mikrotik.cardgenerator.app-main-60:/mipmap-xhdpi/ic_launcher.webp"}, {"merged": "com.mikrotik.cardgenerator.app-release-59:/layout_activity_connection_settings.xml.flat", "source": "com.mikrotik.cardgenerator.app-main-60:/layout/activity_connection_settings.xml"}, {"merged": "com.mikrotik.cardgenerator.app-release-59:/mipmap-hdpi_ic_launcher.webp.flat", "source": "com.mikrotik.cardgenerator.app-main-60:/mipmap-hdpi/ic_launcher.webp"}, {"merged": "com.mikrotik.cardgenerator.app-release-59:/layout_activity_main.xml.flat", "source": "com.mikrotik.cardgenerator.app-main-60:/layout/activity_main.xml"}, {"merged": "com.mikrotik.cardgenerator.app-release-59:/mipmap-anydpi_ic_launcher_round.xml.flat", "source": "com.mikrotik.cardgenerator.app-main-60:/mipmap-anydpi/ic_launcher_round.xml"}, {"merged": "com.mikrotik.cardgenerator.app-release-59:/drawable_ic_import.xml.flat", "source": "com.mikrotik.cardgenerator.app-main-60:/drawable/ic_import.xml"}, {"merged": "com.mikrotik.cardgenerator.app-release-59:/mipmap-xxhdpi_ic_launcher.webp.flat", "source": "com.mikrotik.cardgenerator.app-main-60:/mipmap-xxhdpi/ic_launcher.webp"}, {"merged": "com.mikrotik.cardgenerator.app-release-59:/drawable_ic_help.xml.flat", "source": "com.mikrotik.cardgenerator.app-main-60:/drawable/ic_help.xml"}, {"merged": "com.mikrotik.cardgenerator.app-release-59:/xml_file_paths.xml.flat", "source": "com.mikrotik.cardgenerator.app-main-60:/xml/file_paths.xml"}, {"merged": "com.mikrotik.cardgenerator.app-release-59:/mipmap-anydpi_ic_launcher.xml.flat", "source": "com.mikrotik.cardgenerator.app-main-60:/mipmap-anydpi/ic_launcher.xml"}, {"merged": "com.mikrotik.cardgenerator.app-release-59:/mipmap-mdpi_ic_launcher_round.webp.flat", "source": "com.mikrotik.cardgenerator.app-main-60:/mipmap-mdpi/ic_launcher_round.webp"}, {"merged": "com.mikrotik.cardgenerator.app-release-59:/menu_card_generator_menu.xml.flat", "source": "com.mikrotik.cardgenerator.app-main-60:/menu/card_generator_menu.xml"}, {"merged": "com.mikrotik.cardgenerator.app-release-59:/drawable_ic_hotspot.xml.flat", "source": "com.mikrotik.cardgenerator.app-main-60:/drawable/ic_hotspot.xml"}, {"merged": "com.mikrotik.cardgenerator.app-release-59:/mipmap-hdpi_ic_launcher_round.webp.flat", "source": "com.mikrotik.cardgenerator.app-main-60:/mipmap-hdpi/ic_launcher_round.webp"}, {"merged": "com.mikrotik.cardgenerator.app-release-59:/drawable_ic_launcher_foreground.xml.flat", "source": "com.mikrotik.cardgenerator.app-main-60:/drawable/ic_launcher_foreground.xml"}, {"merged": "com.mikrotik.cardgenerator.app-release-59:/mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "com.mikrotik.cardgenerator.app-main-60:/mipmap-xhdpi/ic_launcher_round.webp"}, {"merged": "com.mikrotik.cardgenerator.app-release-59:/mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "com.mikrotik.cardgenerator.app-main-60:/mipmap-xxhdpi/ic_launcher_round.webp"}, {"merged": "com.mikrotik.cardgenerator.app-release-59:/drawable_ic_launcher_background.xml.flat", "source": "com.mikrotik.cardgenerator.app-main-60:/drawable/ic_launcher_background.xml"}, {"merged": "com.mikrotik.cardgenerator.app-release-59:/mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "com.mikrotik.cardgenerator.app-main-60:/mipmap-xxxhdpi/ic_launcher.webp"}, {"merged": "com.mikrotik.cardgenerator.app-release-59:/xml_data_extraction_rules.xml.flat", "source": "com.mikrotik.cardgenerator.app-main-60:/xml/data_extraction_rules.xml"}, {"merged": "com.mikrotik.cardgenerator.app-release-59:/layout_activity_card_generator.xml.flat", "source": "com.mikrotik.cardgenerator.app-main-60:/layout/activity_card_generator.xml"}, {"merged": "com.mikrotik.cardgenerator.app-release-59:/drawable_ic_user_manager.xml.flat", "source": "com.mikrotik.cardgenerator.app-main-60:/drawable/ic_user_manager.xml"}, {"merged": "com.mikrotik.cardgenerator.app-release-59:/drawable_ic_export.xml.flat", "source": "com.mikrotik.cardgenerator.app-main-60:/drawable/ic_export.xml"}, {"merged": "com.mikrotik.cardgenerator.app-release-59:/mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "com.mikrotik.cardgenerator.app-main-60:/mipmap-xxxhdpi/ic_launcher_round.webp"}, {"merged": "com.mikrotik.cardgenerator.app-release-59:/mipmap-mdpi_ic_launcher.webp.flat", "source": "com.mikrotik.cardgenerator.app-main-60:/mipmap-mdpi/ic_launcher.webp"}, {"merged": "com.mikrotik.cardgenerator.app-release-59:/drawable_ic_arrow_forward.xml.flat", "source": "com.mikrotik.cardgenerator.app-main-60:/drawable/ic_arrow_forward.xml"}, {"merged": "com.mikrotik.cardgenerator.app-release-59:/drawable_ic_settings.xml.flat", "source": "com.mikrotik.cardgenerator.app-main-60:/drawable/ic_settings.xml"}, {"merged": "com.mikrotik.cardgenerator.app-release-59:/xml_backup_rules.xml.flat", "source": "com.mikrotik.cardgenerator.app-main-60:/xml/backup_rules.xml"}]