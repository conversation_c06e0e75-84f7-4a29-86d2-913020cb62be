http://schemas.android.com/apk/res-auto;;${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/colors.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_foreground.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_settings.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_arrow_forward.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_user_manager.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_export.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_hotspot.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_import.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_help.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_mikrotik_logo.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_card_generator.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/card_generator_menu.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_connection_settings.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-anydpi/ic_launcher_round.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher_round.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher_round.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher_round.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher_round.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher_round.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-anydpi/ic_launcher.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/strings.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/themes.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values-night/themes.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/xml/file_paths.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/xml/data_extraction_rules.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/xml/backup_rules.xml,+color:purple_200,0,V4002c05ea,2e002c0614,;"#FFBB86FC";info_color,0,V4000c01be,2c000c01e6,;"#2196F3";overlay_light,0,V4002404e3,**********,;"#80FFFFFF";text_primary,0,V4001402b5,2e001402df,;"#212121";overlay_dark,0,V4002304b2,30002304de,;"#80000000";divider_color,0,V4001b03bd,2f001b03e8,;"#EEEEEE";hover_color,0,V4001e0409,2d001e0432,;"#E3F2FD";selected_color,0,V400200467,**********,;"#E1F5FE";transparent,0,V400290598,2f002905c3,;"#00000000";teal_200,0,V4002f0677,2c002f069f,;"#FF03DAC5";accent_color,0,V4000600e6,2e00060110,;"#F18F01";surface_color,0,V40010023a,2f00100265,;"#FFFFFF";white,0,V40028056e,**********,;"#FFFFFFFF";teal_700,0,V4003006a4,2c003006cc,;"#FF018786";warning_color,0,V4000a0160,2f000a018b,;"#FF9800";secondary_color,0,V4000500b4,31000500e1,;"#A23B72";purple_700,0,V4002e0648,2e002e0672,;"#FF3700B3";primary_dark,0,V400040085,2e000400af,;"#1E5F7A";success_color,0,V400090130,2f0009015b,;"#4CAF50";purple_500,0,V4002d0619,2e002d0643,;"#FF6200EE";pressed_color,0,V4001f0437,2f001f0462,;"#BBDEFB";black,0,V400270544,2900270569,;"#FF000000";card_background,0,V40011026a,3100110297,;"#FFFFFF";border_color,0,V4001a038e,2e001a03b8,;"#E0E0E0";primary_color,0,V400030055,2f00030080,;"#2E86AB";background_color,0,V4000f0207,32000f0235,;"#F5F5F5";text_on_primary,0,V400170341,310017036e,;"#FFFFFF";error_color,0,V4000b0190,2d000b01b9,;"#F44336";text_hint,0,V400160315,2b0016033c,;"#BDBDBD";text_secondary,0,V4001502e4,3000150310,;"#757575";+drawable:ic_launcher_foreground,1,F;ic_settings,2,F;ic_arrow_forward,3,F;ic_user_manager,4,F;ic_launcher_background,5,F;ic_export,6,F;ic_hotspot,7,F;ic_import,8,F;ic_help,9,F;ic_mikrotik_logo,10,F;+id:spinner_profile,11,F;spinner_limit_unit,11,F;action_export,12,F;card_hotspot,13,F;btn_generate,11,F;btn_send_to_mikrotik,11,F;tv_generated_count,11,F;action_settings,12,F;recycler_credentials,11,F;action_help,12,F;progress_bar,11,F;edit_price,11,F;card_generation,11,F;edit_username,11,F;spinner_credential_type,11,F;edit_port,11,F;edit_days,11,F;edit_location,11,F;edit_limit_bytes,11,F;action_import,12,F;btn_export_pdf,11,F;tv_connection_status,11,F;btn_about,13,F;layout_hotspot_fields,11,F;tab_layout,11,F;edit_password,11,F;edit_comment,11,F;btn_save,11,F;card_connection,11,F;edit_prefix,11,F;card_user_manager,13,F;tv_app_version,13,F;edit_ip_address,11,F;edit_suffix,11,F;btn_connect,11,F;edit_length,11,F;card_preview,11,F;edit_count,11,F;btn_settings,13,F;+layout:activity_main,13,F;activity_card_generator,11,F;activity_connection_settings,14,F;+menu:card_generator_menu,12,F;+mipmap:ic_launcher_round,15,F;ic_launcher_round,16,F;ic_launcher_round,17,F;ic_launcher_round,18,F;ic_launcher_round,19,F;ic_launcher_round,20,F;ic_launcher,21,F;ic_launcher,22,F;ic_launcher,23,F;ic_launcher,24,F;ic_launcher,25,F;ic_launcher,26,F;+string:user_manager_title,27,V4000a016f,3e000a01a9,;"👤 User Manager";comment_hint,27,V4002506a3,30002506cf,;"التعليق";settings_button,27,V4001102fd,3800110331,;"⚙️ الإعدادات";permission_storage_title,27,V400480e35,4700480e78,;"إذن الوصول للتخزين";app_version,27,V4000600e5,3300060114,;"الإصدار 2.0";data_type_numbers,27,V4005a119f,33005a11ce,;"أرقام";about_message,27,V400450cae,16f00450e19,;"مولد كروت وسكريبتات MikroTik
الإصدار 2.0

برنامج شامل لتوليد كروت الإنترنت وإدارة المستخدمين
في أجهزة MikroTik RouterOS

الميزات\:
• دعم User Manager و Hotspot
• توليد كروت PDF قابلة للطباعة
• إدارة قاعدة البيانات
• نسخ احتياطية تلقائية
• تصدير واستيراد البيانات
• واجهة عربية سهلة الاستخدام

تطوير\: فريق التطوير";invalid_number,27,V400410c28,3700410c5b,;"رقم غير صحيح";disconnect_button,27,V4001403a2,3b001403d9,;"❌ قطع الاتصال";prefix_hint,27,V40029076b,2f00290796,;"البادئة";invalid_email,27,V4003f0ba8,44003f0be8,;"البريد الإلكتروني غير صحيح";field_required,27,V400400bed,3a00400c23,;"هذا الحقل مطلوب";unit_bytes,27,V4005f1255,2b005f127c,;"بايت";import_button,27,V400180475,34001804a5,;"📥 استيراد";unit_kb,27,V400601281,2c006012a9,;"كيلوبايت";unit_gb,27,V4006212db,2c00621303,;"جيجابايت";hotspot_title,27,V4000d0233,34000d0263,;"🌐 Hotspot";ready_status,27,V400070119,390007014e,;"✅ جاهز للاستخدام";connection_success,27,V4002f083c,3f002f0877,;"تم الاتصال بنجاح";invalid_port,27,V4003c0ae5,3c003c0b1d,;"رقم المنفذ غير صحيح";connection_failed,27,V40030087c,3c003008b4,;"فشل في الاتصال";permission_network_message,27,V4004b0f30,6a004b0f96,;"يحتاج التطبيق للوصول للشبكة للاتصال بأجهزة MikroTik";status_disconnected,27,V4005510cf,3800551103,;"غير متصل";unit_hours,27,V4006813ad,2b006813d4,;"ساعة";yes_button,27,V4001c0538,2a001c055e,;"نعم";export_button,27,V400170442,3200170470,;"📤 تصدير";no_button,27,V4001d0563,28001d0587,;"لا";hotspot_description,27,V4000e0268,7e000e02e2,;"• إدارة نقاط الاتصال اللاسلكي
• تحديد حدود البيانات والوقت
• إنشاء كروت ضيوف";profile_hint,27,V400240670,320024069e,;"البروفايل";export_success,27,V4003509a3,3b003509da,;"تم التصدير بنجاح";user_manager_description,27,V4000b01ae,83000b022d,;"• إدارة المستخدمين في MikroTik
• دعم الإصدارات v6 و v7
• إنشاء حسابات متقدمة";file_type_json,27,V40050101b,330050104a,;"ملف JSON";location_hint,27,V4002606d4,3000260700,;"الموقع";invalid_password,27,V4003e0b65,42003e0ba3,;"كلمة المرور غير صحيحة";unit_minutes,27,V40067137e,2e006713a8,;"دقيقة";generate_button,27,V4001503de,340015040e,;"⚙️ توليد";invalid_ip,27,V4003b0aac,38003b0ae0,;"عنوان IP غير صحيح";file_type_csv,27,V4004f0fe9,31004f1016,;"ملف CSV";status_testing,27,V400571146,3b0057117d,;"جاري الاختبار...";save_failed,27,V40034096e,340034099e,;"فشل في الحفظ";test_button,27,V4001904aa,31001904d7,;"🧪 اختبار";generation_failed,27,V4003208f9,3c00320931,;"فشل في التوليد";export_failed,27,V4003609df,3800360a13,;"فشل في التصدير";password_hint,27,V40023063a,350023066b,;"كلمة المرور";unit_seconds,27,V40066134f,2e00661379,;"ثانية";save_success,27,V400330936,3700330969,;"تم الحفظ بنجاح";main_subtitle,27,V4000500a3,41000500e0,;"اختر نظام التشغيل للبدء";import_failed,27,V400380a56,3a00380a8c,;"فشل في الاستيراد";unit_mb,27,V4006112ae,2c006112d6,;"ميجابايت";suffix_hint,27,V4002a079b,2f002a07c6,;"النهاية";connect_button,27,V40013036e,330013039d,;"🔗 اتصال";email_hint,27,V400270705,3800270739,;"البريد الإلكتروني";cancel_button,27,V4001b0508,2f001b0533,;"إلغاء";unit_days,27,V4006913d9,29006913fe,;"يوم";unit_months,27,V4006b1430,2b006b1457,;"شهر";unit_tb,27,V400631308,2c00631330,;"تيرابايت";invalid_username,27,V4003d0b22,42003d0b60,;"اسم المستخدم غير صحيح";ok_button,27,V4001a04dc,2b001a0503,;"موافق";ip_address_hint,27,V4002005a1,34002005d1,;"عنوان IP";length_hint,27,V4002c07f8,2d002c0821,;"الطول";data_type_letters,27,V4005b11d3,32005b1201,;"حروف";file_type_txt,27,V40051104f,310051107c,;"ملف نصي";save_button,27,V400160413,2e0016043d,;"💾 حفظ";count_hint,27,V4002b07cb,2c002b07f3,;"العدد";file_type_pdf,27,V4004e0fb7,31004e0fe4,;"ملف PDF";app_name,27,V400010010,3700010043,;"مولد كروت MikroTik";about_button,27,V400120336,3700120369,;"ℹ️ حول التطبيق";permission_network_title,27,V4004a0ef0,3f004a0f2b,;"إذن الشبكة";port_hint,27,V4002105d6,2c002105fe,;"المنفذ";generation_success,27,V4003108b9,3f003108f4,;"تم التوليد بنجاح";permission_storage_message,27,V400490e7d,7200490eeb,;"يحتاج التطبيق للوصول للتخزين لحفظ الملفات والنسخ الاحتياطية";username_hint,27,V400220603,3600220635,;"اسم المستخدم";import_success,27,V400370a18,3d00370a51,;"تم الاستيراد بنجاح";about_title,27,V400440c7a,3300440ca9,;"حول التطبيق";price_hint,27,V40028073e,2c00280766,;"السعر";data_type_mixed,27,V4005c1206,31005c1233,;"مختلط";unit_weeks,27,V4006a1403,2c006a142b,;"أسبوع";main_title,27,V400040066,3c0004009e,;"🚀 مولد كروت MikroTik";status_connected,27,V40054109d,31005410ca,;"متصل";status_connecting,27,V400561108,3d00561141,;"جاري الاتصال...";+style:Base.Theme.MikroTikCardGenerator,28,V400020064,c001604bc,;DTheme.Material3.DayNight,colorPrimary:@color/primary_color,colorPrimaryDark:@color/primary_dark,colorAccent:@color/accent_color,colorSecondary:@color/secondary_color,colorSurface:@color/surface_color,colorOnPrimary:@color/text_on_primary,colorOnSurface:@color/text_primary,android\:colorBackground:@color/background_color,android\:textColorPrimary:@color/text_primary,android\:textColorSecondary:@color/text_secondary,android\:textColorHint:@color/text_hint,android\:layoutDirection:rtl,android\:textDirection:rtl,android\:fontFamily:sans-serif,;Theme.MyApplication,28,V4001b0544,4d001b058d,;DTheme.MikroTikCardGenerator,;Theme.MyApplication,29,V400020064,c000e0322,;DTheme.MaterialComponents.DayNight.DarkActionBar,colorPrimary:@color/purple_200,colorPrimaryVariant:@color/purple_700,colorOnPrimary:@color/black,colorSecondary:@color/teal_200,colorSecondaryVariant:@color/teal_200,colorOnSecondary:@color/black,android\:statusBarColor:?attr/colorPrimaryVariant,;Theme.MikroTikCardGenerator,28,V4001804c2,5a00180518,;DBase.Theme.MikroTikCardGenerator,;+xml:file_paths,30,F;data_extraction_rules,31,F;backup_rules,32,F;