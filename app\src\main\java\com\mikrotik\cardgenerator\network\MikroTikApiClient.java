package com.mikrotik.cardgenerator.network;

import android.content.Context;
import com.mikrotik.cardgenerator.data.models.ConnectionSettings;
import com.mikrotik.cardgenerator.data.models.UserManagerCredential;
import com.mikrotik.cardgenerator.data.models.HotspotCredential;
import timber.log.Timber;

import java.io.*;
import java.net.Socket;
import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * عميل API للاتصال بـ MikroTik RouterOS
 */
public class MikroTikApiClient {
    
    private Context context;
    private Socket socket;
    private InputStream inputStream;
    private OutputStream outputStream;
    private boolean isConnected = false;
    private ConnectionSettings connectionSettings;
    
    public MikroTikApiClient(Context context) {
        this.context = context;
    }
    
    /**
     * الاتصال بـ MikroTik
     */
    public boolean connect(ConnectionSettings settings) {
        try {
            this.connectionSettings = settings;
            
            Timber.i("محاولة الاتصال بـ " + settings.getIpAddress() + ":" + settings.getPort());
            
            socket = new Socket(settings.getIpAddress(), settings.getPort());
            socket.setSoTimeout(settings.getTimeout() * 1000);
            
            inputStream = socket.getInputStream();
            outputStream = socket.getOutputStream();
            
            // تسجيل الدخول
            boolean loginSuccess = login(settings.getUsername(), settings.getPassword());
            
            if (loginSuccess) {
                isConnected = true;
                Timber.i("تم الاتصال بنجاح مع " + settings.getIpAddress());
                return true;
            } else {
                disconnect();
                Timber.e("فشل في تسجيل الدخول");
                return false;
            }
            
        } catch (Exception e) {
            Timber.e(e, "خطأ في الاتصال بـ MikroTik");
            disconnect();
            return false;
        }
    }
    
    /**
     * قطع الاتصال
     */
    public void disconnect() {
        try {
            isConnected = false;
            
            if (outputStream != null) {
                outputStream.close();
            }
            if (inputStream != null) {
                inputStream.close();
            }
            if (socket != null) {
                socket.close();
            }
            
            Timber.i("تم قطع الاتصال");
            
        } catch (Exception e) {
            Timber.e(e, "خطأ في قطع الاتصال");
        }
    }
    
    /**
     * تسجيل الدخول
     */
    private boolean login(String username, String password) {
        try {
            // إرسال أمر تسجيل الدخول
            List<String> loginCommand = new ArrayList<>();
            loginCommand.add("/login");
            loginCommand.add("=name=" + username);
            loginCommand.add("=password=" + password);
            
            sendCommand(loginCommand);
            List<String> response = readResponse();
            
            // التحقق من الاستجابة
            for (String line : response) {
                if (line.startsWith("!done")) {
                    return true;
                } else if (line.startsWith("!trap")) {
                    return false;
                }
            }
            
            return false;
            
        } catch (Exception e) {
            Timber.e(e, "خطأ في تسجيل الدخول");
            return false;
        }
    }
    
    /**
     * إضافة مستخدم User Manager
     */
    public boolean addUserManagerUser(UserManagerCredential credential) {
        try {
            if (!isConnected) {
                Timber.e("غير متصل بـ MikroTik");
                return false;
            }
            
            List<String> command = new ArrayList<>();
            command.add("/user-manager/user/add");
            command.add("=name=" + credential.getUsername());
            command.add("=password=" + credential.getPassword());
            
            if (credential.getProfile() != null && !credential.getProfile().isEmpty()) {
                command.add("=group=" + credential.getProfile());
            }
            
            if (credential.getComment() != null && !credential.getComment().isEmpty()) {
                command.add("=comment=" + credential.getComment());
            }
            
            if (credential.getEmail() != null && !credential.getEmail().isEmpty()) {
                command.add("=email=" + credential.getEmail());
            }
            
            sendCommand(command);
            List<String> response = readResponse();
            
            return isSuccessResponse(response);
            
        } catch (Exception e) {
            Timber.e(e, "خطأ في إضافة مستخدم User Manager");
            return false;
        }
    }
    
    /**
     * إضافة مستخدم Hotspot
     */
    public boolean addHotspotUser(HotspotCredential credential) {
        try {
            if (!isConnected) {
                Timber.e("غير متصل بـ MikroTik");
                return false;
            }
            
            List<String> command = new ArrayList<>();
            command.add("/ip/hotspot/user/add");
            command.add("=name=" + credential.getUsername());
            command.add("=password=" + credential.getPassword());
            
            if (credential.getProfile() != null && !credential.getProfile().isEmpty()) {
                command.add("=profile=" + credential.getProfile());
            }
            
            if (credential.getComment() != null && !credential.getComment().isEmpty()) {
                command.add("=comment=" + credential.getComment());
            }
            
            if (credential.getLimitBytes() != null && !credential.getLimitBytes().isEmpty()) {
                String limit = credential.getLimitBytes();
                if (credential.getLimitUnit() != null) {
                    limit += credential.getLimitUnit();
                }
                command.add("=limit-bytes-total=" + limit);
            }
            
            if (credential.getDays() != null && !credential.getDays().isEmpty()) {
                command.add("=limit-uptime=" + credential.getDays() + "d");
            }
            
            sendCommand(command);
            List<String> response = readResponse();
            
            return isSuccessResponse(response);
            
        } catch (Exception e) {
            Timber.e(e, "خطأ في إضافة مستخدم Hotspot");
            return false;
        }
    }
    
    /**
     * إرسال أمر
     */
    private void sendCommand(List<String> command) throws IOException {
        for (String line : command) {
            sendWord(line);
        }
        sendWord(""); // إنهاء الأمر
    }
    
    /**
     * إرسال كلمة
     */
    private void sendWord(String word) throws IOException {
        byte[] wordBytes = word.getBytes("UTF-8");
        sendLength(wordBytes.length);
        outputStream.write(wordBytes);
    }
    
    /**
     * إرسال طول البيانات
     */
    private void sendLength(int length) throws IOException {
        if (length < 0x80) {
            outputStream.write(length);
        } else if (length < 0x4000) {
            outputStream.write(((length >> 8) & 0xFF) | 0x80);
            outputStream.write(length & 0xFF);
        } else if (length < 0x200000) {
            outputStream.write(((length >> 16) & 0xFF) | 0xC0);
            outputStream.write((length >> 8) & 0xFF);
            outputStream.write(length & 0xFF);
        } else if (length < 0x10000000) {
            outputStream.write(((length >> 24) & 0xFF) | 0xE0);
            outputStream.write((length >> 16) & 0xFF);
            outputStream.write((length >> 8) & 0xFF);
            outputStream.write(length & 0xFF);
        } else {
            outputStream.write(0xF0);
            outputStream.write((length >> 24) & 0xFF);
            outputStream.write((length >> 16) & 0xFF);
            outputStream.write((length >> 8) & 0xFF);
            outputStream.write(length & 0xFF);
        }
    }
    
    /**
     * قراءة الاستجابة
     */
    private List<String> readResponse() throws IOException {
        List<String> response = new ArrayList<>();
        String word;
        
        while (!(word = readWord()).isEmpty()) {
            response.add(word);
        }
        
        return response;
    }
    
    /**
     * قراءة كلمة
     */
    private String readWord() throws IOException {
        int length = readLength();
        if (length == 0) {
            return "";
        }
        
        byte[] buffer = new byte[length];
        int totalRead = 0;
        
        while (totalRead < length) {
            int read = inputStream.read(buffer, totalRead, length - totalRead);
            if (read == -1) {
                throw new IOException("انتهى التدفق بشكل غير متوقع");
            }
            totalRead += read;
        }
        
        return new String(buffer, "UTF-8");
    }
    
    /**
     * قراءة طول البيانات
     */
    private int readLength() throws IOException {
        int firstByte = inputStream.read();
        if (firstByte == -1) {
            throw new IOException("انتهى التدفق");
        }
        
        if ((firstByte & 0x80) == 0) {
            return firstByte;
        } else if ((firstByte & 0xC0) == 0x80) {
            return ((firstByte & 0x3F) << 8) | inputStream.read();
        } else if ((firstByte & 0xE0) == 0xC0) {
            return ((firstByte & 0x1F) << 16) | (inputStream.read() << 8) | inputStream.read();
        } else if ((firstByte & 0xF0) == 0xE0) {
            return ((firstByte & 0x0F) << 24) | (inputStream.read() << 16) | 
                   (inputStream.read() << 8) | inputStream.read();
        } else if ((firstByte & 0xF8) == 0xF0) {
            return (inputStream.read() << 24) | (inputStream.read() << 16) | 
                   (inputStream.read() << 8) | inputStream.read();
        }
        
        throw new IOException("تنسيق طول غير صحيح");
    }
    
    /**
     * التحقق من نجاح الاستجابة
     */
    private boolean isSuccessResponse(List<String> response) {
        for (String line : response) {
            if (line.startsWith("!done")) {
                return true;
            } else if (line.startsWith("!trap")) {
                return false;
            }
        }
        return false;
    }
    
    /**
     * اختبار الاتصال
     */
    public boolean testConnection() {
        try {
            if (!isConnected) {
                return false;
            }
            
            List<String> command = new ArrayList<>();
            command.add("/system/identity/print");
            
            sendCommand(command);
            List<String> response = readResponse();
            
            return isSuccessResponse(response);
            
        } catch (Exception e) {
            Timber.e(e, "خطأ في اختبار الاتصال");
            return false;
        }
    }
    
    /**
     * الحصول على معلومات النظام
     */
    public Map<String, String> getSystemInfo() {
        try {
            Map<String, String> info = new HashMap<>();
            
            if (!isConnected) {
                return info;
            }
            
            List<String> command = new ArrayList<>();
            command.add("/system/resource/print");
            
            sendCommand(command);
            List<String> response = readResponse();
            
            for (String line : response) {
                if (line.startsWith("=")) {
                    String[] parts = line.substring(1).split("=", 2);
                    if (parts.length == 2) {
                        info.put(parts[0], parts[1]);
                    }
                }
            }
            
            return info;
            
        } catch (Exception e) {
            Timber.e(e, "خطأ في الحصول على معلومات النظام");
            return new HashMap<>();
        }
    }
    
    /**
     * التحقق من حالة الاتصال
     */
    public boolean isConnected() {
        return isConnected && socket != null && socket.isConnected() && !socket.isClosed();
    }
}
