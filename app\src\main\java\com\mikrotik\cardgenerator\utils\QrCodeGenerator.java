package com.mikrotik.cardgenerator.utils;

import android.graphics.Bitmap;
import android.graphics.Color;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.WriterException;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import timber.log.Timber;

import java.util.HashMap;
import java.util.Map;

/**
 * مولد رموز QR
 */
public class QrCodeGenerator {
    
    /**
     * إنشاء رمز QR
     */
    public static Bitmap generateQrCode(String data, int width, int height) {
        try {
            QRCodeWriter writer = new QRCodeWriter();
            
            // إعداد خيارات QR Code
            Map<EncodeHintType, Object> hints = new HashMap<>();
            hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.M);
            hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
            hints.put(EncodeHintType.MARGIN, 1);
            
            BitMatrix bitMatrix = writer.encode(data, BarcodeFormat.QR_CODE, width, height, hints);
            
            // تحويل BitMatrix إلى Bitmap
            Bitmap bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.RGB_565);
            
            for (int x = 0; x < width; x++) {
                for (int y = 0; y < height; y++) {
                    bitmap.setPixel(x, y, bitMatrix.get(x, y) ? Color.BLACK : Color.WHITE);
                }
            }
            
            return bitmap;
            
        } catch (WriterException e) {
            Timber.e(e, "خطأ في إنشاء رمز QR");
            return null;
        }
    }
    
    /**
     * إنشاء رمز QR لبيانات اعتماد User Manager
     */
    public static Bitmap generateUserManagerQrCode(String username, String password, 
                                                  String profile, int size) {
        String qrData = String.format(
            "نوع: User Manager\nالمستخدم: %s\nكلمة المرور: %s\nالبروفايل: %s",
            username, password, profile != null ? profile : ""
        );
        
        return generateQrCode(qrData, size, size);
    }
    
    /**
     * إنشاء رمز QR لبيانات اعتماد Hotspot
     */
    public static Bitmap generateHotspotQrCode(String username, String password, 
                                             String profile, String limit, int size) {
        String qrData = String.format(
            "نوع: Hotspot\nالمستخدم: %s\nكلمة المرور: %s\nالبروفايل: %s\nالحد: %s",
            username, password, 
            profile != null ? profile : "",
            limit != null ? limit : ""
        );
        
        return generateQrCode(qrData, size, size);
    }
    
    /**
     * إنشاء رمز QR لبيانات الاتصال
     */
    public static Bitmap generateConnectionQrCode(String ipAddress, String username, 
                                                String password, int port, int size) {
        String qrData = String.format(
            "نوع: اتصال MikroTik\nIP: %s\nالمنفذ: %d\nالمستخدم: %s\nكلمة المرور: %s",
            ipAddress, port, username, password
        );
        
        return generateQrCode(qrData, size, size);
    }
    
    /**
     * إنشاء رمز QR مخصص مع ألوان
     */
    public static Bitmap generateColoredQrCode(String data, int width, int height, 
                                             int foregroundColor, int backgroundColor) {
        try {
            QRCodeWriter writer = new QRCodeWriter();
            
            Map<EncodeHintType, Object> hints = new HashMap<>();
            hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.M);
            hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
            hints.put(EncodeHintType.MARGIN, 1);
            
            BitMatrix bitMatrix = writer.encode(data, BarcodeFormat.QR_CODE, width, height, hints);
            
            Bitmap bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.RGB_565);
            
            for (int x = 0; x < width; x++) {
                for (int y = 0; y < height; y++) {
                    bitmap.setPixel(x, y, bitMatrix.get(x, y) ? foregroundColor : backgroundColor);
                }
            }
            
            return bitmap;
            
        } catch (WriterException e) {
            Timber.e(e, "خطأ في إنشاء رمز QR ملون");
            return null;
        }
    }
    
    /**
     * حفظ رمز QR كملف
     */
    public static boolean saveQrCodeToFile(Bitmap qrBitmap, String fileName) {
        try {
            java.io.File file = new java.io.File(FileUtils.getExportsDirectory(), fileName + ".png");
            java.io.FileOutputStream out = new java.io.FileOutputStream(file);
            qrBitmap.compress(Bitmap.CompressFormat.PNG, 100, out);
            out.close();
            
            Timber.i("تم حفظ رمز QR: " + file.getAbsolutePath());
            return true;
            
        } catch (Exception e) {
            Timber.e(e, "خطأ في حفظ رمز QR");
            return false;
        }
    }
}
