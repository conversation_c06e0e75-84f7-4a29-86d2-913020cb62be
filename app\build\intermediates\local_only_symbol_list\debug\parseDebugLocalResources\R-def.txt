R_DEF: Internal format may change without notice
local
color accent_color
color background_color
color black
color border_color
color card_background
color divider_color
color error_color
color hover_color
color info_color
color overlay_dark
color overlay_light
color pressed_color
color primary_color
color primary_dark
color purple_200
color purple_500
color purple_700
color secondary_color
color selected_color
color success_color
color surface_color
color teal_200
color teal_700
color text_hint
color text_on_primary
color text_primary
color text_secondary
color transparent
color warning_color
color white
drawable ic_arrow_forward
drawable ic_export
drawable ic_help
drawable ic_hotspot
drawable ic_import
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable ic_mikrotik_logo
drawable ic_settings
drawable ic_user_manager
id action_export
id action_help
id action_import
id action_settings
id btn_about
id btn_connect
id btn_export_pdf
id btn_generate
id btn_save
id btn_send_to_mikrotik
id btn_settings
id card_connection
id card_generation
id card_hotspot
id card_preview
id card_user_manager
id edit_comment
id edit_count
id edit_days
id edit_ip_address
id edit_length
id edit_limit_bytes
id edit_location
id edit_password
id edit_port
id edit_prefix
id edit_price
id edit_suffix
id edit_username
id layout_hotspot_fields
id progress_bar
id recycler_credentials
id spinner_credential_type
id spinner_limit_unit
id spinner_profile
id tab_layout
id tv_app_version
id tv_connection_status
id tv_generated_count
layout activity_card_generator
layout activity_connection_settings
layout activity_main
menu card_generator_menu
mipmap ic_launcher
mipmap ic_launcher_round
string about_button
string about_message
string about_title
string app_name
string app_version
string cancel_button
string comment_hint
string connect_button
string connection_failed
string connection_success
string count_hint
string data_type_letters
string data_type_mixed
string data_type_numbers
string disconnect_button
string email_hint
string export_button
string export_failed
string export_success
string field_required
string file_type_csv
string file_type_json
string file_type_pdf
string file_type_txt
string generate_button
string generation_failed
string generation_success
string hotspot_description
string hotspot_title
string import_button
string import_failed
string import_success
string invalid_email
string invalid_ip
string invalid_number
string invalid_password
string invalid_port
string invalid_username
string ip_address_hint
string length_hint
string location_hint
string main_subtitle
string main_title
string no_button
string ok_button
string password_hint
string permission_network_message
string permission_network_title
string permission_storage_message
string permission_storage_title
string port_hint
string prefix_hint
string price_hint
string profile_hint
string ready_status
string save_button
string save_failed
string save_success
string settings_button
string status_connected
string status_connecting
string status_disconnected
string status_testing
string suffix_hint
string test_button
string unit_bytes
string unit_days
string unit_gb
string unit_hours
string unit_kb
string unit_mb
string unit_minutes
string unit_months
string unit_seconds
string unit_tb
string unit_weeks
string user_manager_description
string user_manager_title
string username_hint
string yes_button
style Base.Theme.MikroTikCardGenerator
style Theme.MikroTikCardGenerator
style Theme.MyApplication
xml backup_rules
xml data_extraction_rules
xml file_paths
