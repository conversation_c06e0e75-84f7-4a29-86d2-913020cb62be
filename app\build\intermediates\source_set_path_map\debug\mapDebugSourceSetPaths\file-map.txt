com.mikrotik.cardgenerator.app-databinding-adapters-8.10.1-0 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\03ad468526512b2d0c733bef52f114c1\transformed\databinding-adapters-8.10.1\res
com.mikrotik.cardgenerator.app-activity-1.8.2-1 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\074795499c0b21a181dac2f13b6e4db7\transformed\activity-1.8.2\res
com.mikrotik.cardgenerator.app-drawerlayout-1.1.1-2 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\07837f2e8156f753da4c078e03a0a781\transformed\drawerlayout-1.1.1\res
com.mikrotik.cardgenerator.app-sqlite-framework-2.4.0-3 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f385c7feb39f5b55436080f17363bbe\transformed\sqlite-framework-2.4.0\res
com.mikrotik.cardgenerator.app-window-1.0.0-4 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\13a59fa95409d2015886ebe40fb0f314\transformed\window-1.0.0\res
com.mikrotik.cardgenerator.app-annotation-experimental-1.4.0-5 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\141e5df82a74df8db75327ae384ddd77\transformed\annotation-experimental-1.4.0\res
com.mikrotik.cardgenerator.app-zxing-android-embedded-4.3.0-6 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1772ff301a09aacb61759aedab678ec4\transformed\zxing-android-embedded-4.3.0\res
com.mikrotik.cardgenerator.app-activity-ktx-1.8.2-7 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18dff8844659d88544fd10752215e9db\transformed\activity-ktx-1.8.2\res
com.mikrotik.cardgenerator.app-recyclerview-1.3.2-8 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\196182f12a0590650d2fccf6bae901f7\transformed\recyclerview-1.3.2\res
com.mikrotik.cardgenerator.app-slidingpanelayout-1.2.0-9 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1af537cd1eaa2f03445a4275856fd0e8\transformed\slidingpanelayout-1.2.0\res
com.mikrotik.cardgenerator.app-navigation-common-2.7.6-10 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b835da36471aad2f6572f1ca1bdc834\transformed\navigation-common-2.7.6\res
com.mikrotik.cardgenerator.app-lifecycle-viewmodel-savedstate-2.7.0-11 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\********************************\transformed\lifecycle-viewmodel-savedstate-2.7.0\res
com.mikrotik.cardgenerator.app-navigation-fragment-ktx-2.7.6-12 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ff8368eeadc41cc0c4dc3bbfa002e4\transformed\navigation-fragment-ktx-2.7.6\res
com.mikrotik.cardgenerator.app-core-runtime-2.2.0-13 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fab9a54ce3385639096711eb292e97a\transformed\core-runtime-2.2.0\res
com.mikrotik.cardgenerator.app-security-crypto-1.1.0-alpha06-14 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3746766d48586e8a0b440c01af21adb6\transformed\security-crypto-1.1.0-alpha06\res
com.mikrotik.cardgenerator.app-viewpager2-1.1.0-beta02-15 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3a184e2e283937503cd0cde0039925b2\transformed\viewpager2-1.1.0-beta02\res
com.mikrotik.cardgenerator.app-lifecycle-viewmodel-ktx-2.7.0-16 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3edb0cfe1a653444ea1594264731104b\transformed\lifecycle-viewmodel-ktx-2.7.0\res
com.mikrotik.cardgenerator.app-appcompat-1.7.1-17 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\412d4fb45e796eeac261181f9a418e93\transformed\appcompat-1.7.1\res
com.mikrotik.cardgenerator.app-navigation-common-ktx-2.7.6-18 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\42349ef91e0ffdf10c447e49c957def5\transformed\navigation-common-ktx-2.7.6\res
com.mikrotik.cardgenerator.app-core-1.13.0-19 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476525ada1d35df1ee329f5bd94fbe69\transformed\core-1.13.0\res
com.mikrotik.cardgenerator.app-navigation-fragment-2.7.6-20 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4fbd26ed86ce5301ee58995ba0b3e270\transformed\navigation-fragment-2.7.6\res
com.mikrotik.cardgenerator.app-savedstate-1.2.1-21 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\516fab528387353e884be276c786c743\transformed\savedstate-1.2.1\res
com.mikrotik.cardgenerator.app-constraintlayout-2.1.4-22 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5acc1f01c634a190d2cd8f2a0386606b\transformed\constraintlayout-2.1.4\res
com.mikrotik.cardgenerator.app-lifecycle-livedata-core-2.7.0-23 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b503b1c5153664876404837caa25b27\transformed\lifecycle-livedata-core-2.7.0\res
com.mikrotik.cardgenerator.app-lifecycle-livedata-2.7.0-24 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5cff193f4764a10eb7b8ae4bf674e29c\transformed\lifecycle-livedata-2.7.0\res
com.mikrotik.cardgenerator.app-lifecycle-livedata-core-ktx-2.7.0-25 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dfe415294ed1c71c6ed44b249ac45b2\transformed\lifecycle-livedata-core-ktx-2.7.0\res
com.mikrotik.cardgenerator.app-lifecycle-livedata-ktx-2.7.0-26 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ff68d601e50eefe467e91bee9ad1cab\transformed\lifecycle-livedata-ktx-2.7.0\res
com.mikrotik.cardgenerator.app-dexter-6.2.3-27 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69e6f47866a4eaec992fd3042f3973fa\transformed\dexter-6.2.3\res
com.mikrotik.cardgenerator.app-material-1.12.0-28 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69e8a0e0d964fb1e9e45715fb440dd3e\transformed\material-1.12.0\res
com.mikrotik.cardgenerator.app-lifecycle-runtime-2.7.0-29 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6aa93a31e99238e5a03037a6be2d1511\transformed\lifecycle-runtime-2.7.0\res
com.mikrotik.cardgenerator.app-cardview-1.0.0-30 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d875b1c0926ce30f0c2c140ec9a416f\transformed\cardview-1.0.0\res
com.mikrotik.cardgenerator.app-navigation-runtime-ktx-2.7.6-31 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e372d3c136a358d432efd8645459bd4\transformed\navigation-runtime-ktx-2.7.6\res
com.mikrotik.cardgenerator.app-lifecycle-runtime-ktx-2.7.0-32 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\72bad607570cad94d90dffe69a9cc5f2\transformed\lifecycle-runtime-ktx-2.7.0\res
com.mikrotik.cardgenerator.app-databinding-runtime-8.10.1-33 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7745f3fe7d50c18d02226dc83cf21b53\transformed\databinding-runtime-8.10.1\res
com.mikrotik.cardgenerator.app-coordinatorlayout-1.1.0-34 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c3cf3ea01e42faaf84f49914b8ccb32\transformed\coordinatorlayout-1.1.0\res
com.mikrotik.cardgenerator.app-savedstate-ktx-1.2.1-35 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\811b5222c4c72b024aeab07485e7b035\transformed\savedstate-ktx-1.2.1\res
com.mikrotik.cardgenerator.app-navigation-runtime-2.7.6-36 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82fe846eeaeb02c33b78bde3efbb65b5\transformed\navigation-runtime-2.7.6\res
com.mikrotik.cardgenerator.app-glide-4.16.0-37 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85a983c53670fb013650d4ad7408b5dd\transformed\glide-4.16.0\res
com.mikrotik.cardgenerator.app-fragment-ktx-1.6.2-38 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\86b179ede1d23b0cb81249a2dd0aaafe\transformed\fragment-ktx-1.6.2\res
com.mikrotik.cardgenerator.app-customview-poolingcontainer-1.0.0-39 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87539e0838a6a4821f42a9630f42225f\transformed\customview-poolingcontainer-1.0.0\res
com.mikrotik.cardgenerator.app-navigation-ui-ktx-2.7.6-40 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94fbd0343f6b161191ddfeafc05e2fc8\transformed\navigation-ui-ktx-2.7.6\res
com.mikrotik.cardgenerator.app-fragment-1.6.2-41 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\982ea38df7d0ad5d60b90e7f1cba56d5\transformed\fragment-1.6.2\res
com.mikrotik.cardgenerator.app-sqlite-2.4.0-42 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b2c9c3712cff616c3c19c54fccc59c4e\transformed\sqlite-2.4.0\res
com.mikrotik.cardgenerator.app-lifecycle-process-2.7.0-43 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bad75f318ec05692b92dc56f1a7f9d6b\transformed\lifecycle-process-2.7.0\res
com.mikrotik.cardgenerator.app-startup-runtime-1.1.1-44 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf37978e2ace7eb5792125215edb1520\transformed\startup-runtime-1.1.1\res
com.mikrotik.cardgenerator.app-core-ktx-1.13.0-45 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf5cd4c4348b3a7d572c1eae144ce987\transformed\core-ktx-1.13.0\res
com.mikrotik.cardgenerator.app-emoji2-1.3.0-46 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c97984e08a3d1ad94cf20e956055eb58\transformed\emoji2-1.3.0\res
com.mikrotik.cardgenerator.app-lifecycle-viewmodel-2.7.0-47 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc944ba4f9b367f03946192560ff3342\transformed\lifecycle-viewmodel-2.7.0\res
com.mikrotik.cardgenerator.app-appcompat-resources-1.7.1-48 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6cdabe0e02411696034863da461e405\transformed\appcompat-resources-1.7.1\res
com.mikrotik.cardgenerator.app-room-runtime-2.6.1-49 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e82a49340ca0c527f750264087cd9c54\transformed\room-runtime-2.6.1\res
com.mikrotik.cardgenerator.app-emoji2-views-helper-1.3.0-50 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eb8002ca9cdd017c9d565e15d9f04159\transformed\emoji2-views-helper-1.3.0\res
com.mikrotik.cardgenerator.app-navigation-ui-2.7.6-51 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb7148cc8318b09aaa0d36d8e3e2b12c\transformed\navigation-ui-2.7.6\res
com.mikrotik.cardgenerator.app-transition-1.5.0-52 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd51dabcb2567111cf2080f7a6c60e47\transformed\transition-1.5.0\res
com.mikrotik.cardgenerator.app-room-ktx-2.6.1-53 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fde277bcb9660a699dd7e88ddd638697\transformed\room-ktx-2.6.1\res
com.mikrotik.cardgenerator.app-profileinstaller-1.3.1-54 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe3eb6ea41676c16cb321c4bcd315165\transformed\profileinstaller-1.3.1\res
com.mikrotik.cardgenerator.app-pngs-55 C:\Users\<USER>\Desktop\New folder (4)\app\build\generated\res\pngs\debug
com.mikrotik.cardgenerator.app-resValues-56 C:\Users\<USER>\Desktop\New folder (4)\app\build\generated\res\resValues\debug
com.mikrotik.cardgenerator.app-packageDebugResources-57 C:\Users\<USER>\Desktop\New folder (4)\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.mikrotik.cardgenerator.app-packageDebugResources-58 C:\Users\<USER>\Desktop\New folder (4)\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.mikrotik.cardgenerator.app-debug-59 C:\Users\<USER>\Desktop\New folder (4)\app\build\intermediates\merged_res\debug\mergeDebugResources
com.mikrotik.cardgenerator.app-debug-60 C:\Users\<USER>\Desktop\New folder (4)\app\src\debug\res
com.mikrotik.cardgenerator.app-main-61 C:\Users\<USER>\Desktop\New folder (4)\app\src\main\res
