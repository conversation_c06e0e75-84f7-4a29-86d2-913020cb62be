package com.mikrotik.cardgenerator.data.models;

/**
 * نموذج بيانات إعدادات الاتصال بـ MikroTik
 */
public class ConnectionSettings {
    
    private String ipAddress;
    private int port;
    private String username;
    private String password;
    private boolean useSSL;
    private int timeout;
    private boolean savePassword;
    private String connectionName;
    private boolean isDefault;
    
    // Constructors
    public ConnectionSettings() {
        this.port = 8728; // المنفذ الافتراضي لـ MikroTik API
        this.timeout = 30; // 30 ثانية
        this.useSSL = false;
        this.savePassword = false;
        this.isDefault = false;
    }
    
    public ConnectionSettings(String ipAddress, String username, String password) {
        this();
        this.ipAddress = ipAddress;
        this.username = username;
        this.password = password;
    }
    
    // Getters and Setters
    public String getIpAddress() {
        return ipAddress;
    }
    
    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }
    
    public int getPort() {
        return port;
    }
    
    public void setPort(int port) {
        this.port = port;
    }
    
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    public String getPassword() {
        return password;
    }
    
    public void setPassword(String password) {
        this.password = password;
    }
    
    public boolean isUseSSL() {
        return useSSL;
    }
    
    public void setUseSSL(boolean useSSL) {
        this.useSSL = useSSL;
    }
    
    public int getTimeout() {
        return timeout;
    }
    
    public void setTimeout(int timeout) {
        this.timeout = timeout;
    }
    
    public boolean isSavePassword() {
        return savePassword;
    }
    
    public void setSavePassword(boolean savePassword) {
        this.savePassword = savePassword;
    }
    
    public String getConnectionName() {
        return connectionName;
    }
    
    public void setConnectionName(String connectionName) {
        this.connectionName = connectionName;
    }
    
    public boolean isDefault() {
        return isDefault;
    }
    
    public void setDefault(boolean aDefault) {
        isDefault = aDefault;
    }
    
    /**
     * التحقق من صحة البيانات
     */
    public boolean isValid() {
        return ipAddress != null && !ipAddress.trim().isEmpty() &&
               username != null && !username.trim().isEmpty() &&
               port > 0 && port <= 65535;
    }
    
    /**
     * الحصول على عنوان الاتصال الكامل
     */
    public String getFullAddress() {
        return ipAddress + ":" + port;
    }
    
    @Override
    public String toString() {
        return "ConnectionSettings{" +
                "ipAddress='" + ipAddress + '\'' +
                ", port=" + port +
                ", username='" + username + '\'' +
                ", useSSL=" + useSSL +
                ", timeout=" + timeout +
                ", connectionName='" + connectionName + '\'' +
                ", isDefault=" + isDefault +
                '}';
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        ConnectionSettings that = (ConnectionSettings) obj;
        
        if (port != that.port) return false;
        if (ipAddress != null ? !ipAddress.equals(that.ipAddress) : that.ipAddress != null)
            return false;
        return username != null ? username.equals(that.username) : that.username == null;
    }
    
    @Override
    public int hashCode() {
        int result = ipAddress != null ? ipAddress.hashCode() : 0;
        result = 31 * result + port;
        result = 31 * result + (username != null ? username.hashCode() : 0);
        return result;
    }
}
