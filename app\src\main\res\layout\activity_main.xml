<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_color"
    tools:context=".ui.MainActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- العنوان الرئيسي -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:layout_marginBottom="32dp">

            <ImageView
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:src="@drawable/ic_mikrotik_logo"
                android:layout_marginBottom="16dp"
                android:contentDescription="شعار MikroTik" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="🚀 مولد كروت MikroTik"
                android:textSize="24sp"
                android:textStyle="bold"
                android:textColor="@color/primary_color"
                android:gravity="center" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="اختر نظام التشغيل للبدء"
                android:textSize="16sp"
                android:textColor="@color/text_secondary"
                android:layout_marginTop="8dp"
                android:gravity="center" />

        </LinearLayout>

        <!-- كروت اختيار النظام -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginBottom="24dp">

            <!-- كارت User Manager -->
            <androidx.cardview.widget.CardView
                android:id="@+id/card_user_manager"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                android:clickable="true"
                android:focusable="true"
                android:foreground="?android:attr/selectableItemBackground">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="20dp">

                    <ImageView
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:src="@drawable/ic_user_manager"
                        android:layout_marginEnd="16dp"
                        android:contentDescription="User Manager" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="👤 User Manager"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="@color/text_primary" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="• إدارة المستخدمين في MikroTik\n• دعم الإصدارات v6 و v7\n• إنشاء حسابات متقدمة"
                            android:textSize="14sp"
                            android:textColor="@color/text_secondary"
                            android:layout_marginTop="4dp" />

                    </LinearLayout>

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_arrow_forward"
                        android:layout_gravity="center_vertical"
                        android:contentDescription="السهم" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- كارت Hotspot -->
            <androidx.cardview.widget.CardView
                android:id="@+id/card_hotspot"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                android:clickable="true"
                android:focusable="true"
                android:foreground="?android:attr/selectableItemBackground">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="20dp">

                    <ImageView
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:src="@drawable/ic_hotspot"
                        android:layout_marginEnd="16dp"
                        android:contentDescription="Hotspot" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="🌐 Hotspot"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="@color/text_primary" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="• إدارة نقاط الاتصال اللاسلكي\n• تحديد حدود البيانات والوقت\n• إنشاء كروت ضيوف"
                            android:textSize="14sp"
                            android:textColor="@color/text_secondary"
                            android:layout_marginTop="4dp" />

                    </LinearLayout>

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_arrow_forward"
                        android:layout_gravity="center_vertical"
                        android:contentDescription="السهم" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

        <!-- أزرار الإعدادات -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="16dp">

            <Button
                android:id="@+id/btn_settings"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="⚙️ الإعدادات"
                android:layout_marginEnd="8dp"
                style="@style/Widget.Material3.Button.OutlinedButton" />

            <Button
                android:id="@+id/btn_about"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="ℹ️ حول التطبيق"
                android:layout_marginStart="8dp"
                style="@style/Widget.Material3.Button.OutlinedButton" />

        </LinearLayout>

        <!-- معلومات الحالة -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardCornerRadius="8dp"
            app:cardElevation="2dp"
            android:layout_marginBottom="16dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="16dp"
                android:gravity="center_vertical">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="✅ جاهز للاستخدام"
                    android:textColor="@color/success_color"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tv_app_version"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="الإصدار 2.0"
                    android:textSize="12sp"
                    android:textColor="@color/text_secondary" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

    </LinearLayout>

</ScrollView>
