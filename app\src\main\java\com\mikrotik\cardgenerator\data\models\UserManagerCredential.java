package com.mikrotik.cardgenerator.data.models;

import androidx.room.Entity;
import androidx.room.PrimaryKey;
import androidx.room.ColumnInfo;
import java.util.Date;

/**
 * نموذج بيانات حسابات User Manager
 */
@Entity(tableName = "user_manager_credentials")
public class UserManagerCredential {
    
    @PrimaryKey(autoGenerate = true)
    private int id;
    
    @ColumnInfo(name = "username")
    private String username;
    
    @ColumnInfo(name = "password")
    private String password;
    
    @ColumnInfo(name = "profile")
    private String profile;
    
    @ColumnInfo(name = "comment")
    private String comment;
    
    @ColumnInfo(name = "location")
    private String location;
    
    @ColumnInfo(name = "email")
    private String email;
    
    @ColumnInfo(name = "price")
    private String price;
    
    @ColumnInfo(name = "created_date")
    private Date createdDate;
    
    @ColumnInfo(name = "updated_date")
    private Date updatedDate;
    
    @ColumnInfo(name = "is_active")
    private boolean isActive;
    
    @ColumnInfo(name = "serial_number")
    private int serialNumber;
    
    // Constructors
    public UserManagerCredential() {
        this.createdDate = new Date();
        this.updatedDate = new Date();
        this.isActive = true;
    }
    
    public UserManagerCredential(String username, String password, String profile) {
        this();
        this.username = username;
        this.password = password;
        this.profile = profile;
    }
    
    // Getters and Setters
    public int getId() {
        return id;
    }
    
    public void setId(int id) {
        this.id = id;
    }
    
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    public String getPassword() {
        return password;
    }
    
    public void setPassword(String password) {
        this.password = password;
    }
    
    public String getProfile() {
        return profile;
    }
    
    public void setProfile(String profile) {
        this.profile = profile;
    }
    
    public String getComment() {
        return comment;
    }
    
    public void setComment(String comment) {
        this.comment = comment;
    }
    
    public String getLocation() {
        return location;
    }
    
    public void setLocation(String location) {
        this.location = location;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public String getPrice() {
        return price;
    }
    
    public void setPrice(String price) {
        this.price = price;
    }
    
    public Date getCreatedDate() {
        return createdDate;
    }
    
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }
    
    public Date getUpdatedDate() {
        return updatedDate;
    }
    
    public void setUpdatedDate(Date updatedDate) {
        this.updatedDate = updatedDate;
    }
    
    public boolean isActive() {
        return isActive;
    }
    
    public void setActive(boolean active) {
        isActive = active;
    }
    
    public int getSerialNumber() {
        return serialNumber;
    }
    
    public void setSerialNumber(int serialNumber) {
        this.serialNumber = serialNumber;
    }
    
    @Override
    public String toString() {
        return "UserManagerCredential{" +
                "id=" + id +
                ", username='" + username + '\'' +
                ", profile='" + profile + '\'' +
                ", comment='" + comment + '\'' +
                ", location='" + location + '\'' +
                ", email='" + email + '\'' +
                ", price='" + price + '\'' +
                ", serialNumber=" + serialNumber +
                ", isActive=" + isActive +
                '}';
    }
}
