[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mikrotik.cardgenerator.app-debug-59:\\mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mikrotik.cardgenerator.app-main-61:\\mipmap-xxxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mikrotik.cardgenerator.app-debug-59:\\mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mikrotik.cardgenerator.app-main-61:\\mipmap-xhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mikrotik.cardgenerator.app-debug-59:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mikrotik.cardgenerator.app-main-61:\\xml\\backup_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mikrotik.cardgenerator.app-debug-59:\\layout_activity_card_generator.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mikrotik.cardgenerator.app-main-61:\\layout\\activity_card_generator.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mikrotik.cardgenerator.app-debug-59:\\drawable_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mikrotik.cardgenerator.app-main-61:\\drawable\\ic_launcher_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mikrotik.cardgenerator.app-debug-59:\\drawable_ic_user_manager.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mikrotik.cardgenerator.app-main-61:\\drawable\\ic_user_manager.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mikrotik.cardgenerator.app-debug-59:\\mipmap-mdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mikrotik.cardgenerator.app-main-61:\\mipmap-mdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mikrotik.cardgenerator.app-debug-59:\\drawable_ic_arrow_forward.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mikrotik.cardgenerator.app-main-61:\\drawable\\ic_arrow_forward.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mikrotik.cardgenerator.app-debug-59:\\mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mikrotik.cardgenerator.app-main-61:\\mipmap-xxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mikrotik.cardgenerator.app-debug-59:\\drawable_ic_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mikrotik.cardgenerator.app-main-61:\\drawable\\ic_settings.xml"}, {"merged": "com.mikrotik.cardgenerator.app-debug-59:/layout_activity_connection_settings.xml.flat", "source": "com.mikrotik.cardgenerator.app-main-61:/layout/activity_connection_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mikrotik.cardgenerator.app-debug-59:\\mipmap-anydpi_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mikrotik.cardgenerator.app-main-61:\\mipmap-anydpi\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mikrotik.cardgenerator.app-debug-59:\\drawable_ic_help.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mikrotik.cardgenerator.app-main-61:\\drawable\\ic_help.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mikrotik.cardgenerator.app-debug-59:\\mipmap-xxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mikrotik.cardgenerator.app-main-61:\\mipmap-xxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mikrotik.cardgenerator.app-debug-59:\\xml_file_paths.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mikrotik.cardgenerator.app-main-61:\\xml\\file_paths.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mikrotik.cardgenerator.app-debug-59:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mikrotik.cardgenerator.app-main-61:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mikrotik.cardgenerator.app-debug-59:\\drawable_ic_hotspot.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mikrotik.cardgenerator.app-main-61:\\drawable\\ic_hotspot.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mikrotik.cardgenerator.app-debug-59:\\mipmap-xhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mikrotik.cardgenerator.app-main-61:\\mipmap-xhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mikrotik.cardgenerator.app-debug-59:\\drawable_ic_import.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mikrotik.cardgenerator.app-main-61:\\drawable\\ic_import.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mikrotik.cardgenerator.app-debug-59:\\mipmap-anydpi_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mikrotik.cardgenerator.app-main-61:\\mipmap-anydpi\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mikrotik.cardgenerator.app-debug-59:\\menu_card_generator_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mikrotik.cardgenerator.app-main-61:\\menu\\card_generator_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mikrotik.cardgenerator.app-debug-59:\\mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mikrotik.cardgenerator.app-main-61:\\mipmap-xxxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mikrotik.cardgenerator.app-debug-59:\\mipmap-mdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mikrotik.cardgenerator.app-main-61:\\mipmap-mdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mikrotik.cardgenerator.app-debug-59:\\mipmap-hdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mikrotik.cardgenerator.app-main-61:\\mipmap-hdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mikrotik.cardgenerator.app-debug-59:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mikrotik.cardgenerator.app-main-61:\\drawable\\ic_launcher_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mikrotik.cardgenerator.app-debug-59:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mikrotik.cardgenerator.app-main-61:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mikrotik.cardgenerator.app-debug-59:\\drawable_ic_mikrotik_logo.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mikrotik.cardgenerator.app-main-61:\\drawable\\ic_mikrotik_logo.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mikrotik.cardgenerator.app-debug-59:\\drawable_ic_export.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mikrotik.cardgenerator.app-main-61:\\drawable\\ic_export.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mikrotik.cardgenerator.app-debug-59:\\mipmap-hdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mikrotik.cardgenerator.app-main-61:\\mipmap-hdpi\\ic_launcher.webp"}]