package com.mikrotik.cardgenerator.utils;

import android.content.Context;
import android.os.Environment;
import com.mikrotik.cardgenerator.MikroTikApplication;
import timber.log.Timber;

import java.io.*;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * أدوات التعامل مع الملفات
 */
public class FileUtils {
    
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault());
    
    /**
     * الحصول على مجلد التطبيق الرئيسي
     */
    public static File getAppDirectory() {
        return MikroTikApplication.getInstance().getAppDirectory();
    }
    
    /**
     * الحصول على مجلد التصدير
     */
    public static File getExportsDirectory() {
        return MikroTikApplication.getInstance().getSubDirectory(MikroTikApplication.EXPORTS_FOLDER);
    }
    
    /**
     * الحصول على مجلد النسخ الاحتياطية
     */
    public static File getBackupsDirectory() {
        return MikroTikApplication.getInstance().getSubDirectory(MikroTikApplication.BACKUPS_FOLDER);
    }
    
    /**
     * الحصول على مجلد القوالب
     */
    public static File getTemplatesDirectory() {
        return MikroTikApplication.getInstance().getSubDirectory(MikroTikApplication.TEMPLATES_FOLDER);
    }
    
    /**
     * الحصول على مجلد السجلات
     */
    public static File getLogsDirectory() {
        return MikroTikApplication.getInstance().getSubDirectory(MikroTikApplication.LOGS_FOLDER);
    }
    
    /**
     * إنشاء اسم ملف فريد
     */
    public static String generateUniqueFileName(String prefix, String extension) {
        String timestamp = DATE_FORMAT.format(new Date());
        return prefix + "_" + timestamp + "." + extension;
    }
    
    /**
     * قراءة محتوى ملف
     */
    public static String readFileContent(File file) {
        try {
            BufferedReader reader = new BufferedReader(new FileReader(file));
            StringBuilder content = new StringBuilder();
            String line;
            
            while ((line = reader.readLine()) != null) {
                content.append(line).append("\n");
            }
            
            reader.close();
            return content.toString();
            
        } catch (IOException e) {
            Timber.e(e, "خطأ في قراءة الملف: " + file.getAbsolutePath());
            return "";
        }
    }
    
    /**
     * كتابة محتوى في ملف
     */
    public static boolean writeFileContent(File file, String content) {
        try {
            // إنشاء المجلد إذا لم يكن موجوداً
            File parentDir = file.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                parentDir.mkdirs();
            }
            
            FileWriter writer = new FileWriter(file);
            writer.write(content);
            writer.close();
            
            Timber.i("تم حفظ الملف: " + file.getAbsolutePath());
            return true;
            
        } catch (IOException e) {
            Timber.e(e, "خطأ في كتابة الملف: " + file.getAbsolutePath());
            return false;
        }
    }
    
    /**
     * نسخ ملف
     */
    public static boolean copyFile(File source, File destination) {
        try {
            // إنشاء المجلد الوجهة إذا لم يكن موجوداً
            File parentDir = destination.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                parentDir.mkdirs();
            }
            
            FileInputStream inputStream = new FileInputStream(source);
            FileOutputStream outputStream = new FileOutputStream(destination);
            
            byte[] buffer = new byte[1024];
            int length;
            
            while ((length = inputStream.read(buffer)) > 0) {
                outputStream.write(buffer, 0, length);
            }
            
            inputStream.close();
            outputStream.close();
            
            Timber.i("تم نسخ الملف من " + source.getAbsolutePath() + " إلى " + destination.getAbsolutePath());
            return true;
            
        } catch (IOException e) {
            Timber.e(e, "خطأ في نسخ الملف");
            return false;
        }
    }
    
    /**
     * حذف ملف
     */
    public static boolean deleteFile(File file) {
        try {
            if (file.exists()) {
                boolean deleted = file.delete();
                if (deleted) {
                    Timber.i("تم حذف الملف: " + file.getAbsolutePath());
                }
                return deleted;
            }
            return true;
        } catch (Exception e) {
            Timber.e(e, "خطأ في حذف الملف: " + file.getAbsolutePath());
            return false;
        }
    }
    
    /**
     * الحصول على حجم الملف بصيغة قابلة للقراءة
     */
    public static String getReadableFileSize(long size) {
        if (size <= 0) return "0 B";
        
        final String[] units = new String[]{"B", "KB", "MB", "GB", "TB"};
        int digitGroups = (int) (Math.log10(size) / Math.log10(1024));
        
        return String.format("%.1f %s", 
            size / Math.pow(1024, digitGroups), 
            units[digitGroups]);
    }
    
    /**
     * التحقق من وجود مساحة كافية
     */
    public static boolean hasEnoughSpace(long requiredSpace) {
        try {
            File appDir = getAppDirectory();
            long availableSpace = appDir.getFreeSpace();
            return availableSpace > requiredSpace;
        } catch (Exception e) {
            Timber.e(e, "خطأ في التحقق من المساحة المتاحة");
            return false;
        }
    }
    
    /**
     * تنظيف الملفات المؤقتة
     */
    public static void cleanTempFiles() {
        try {
            File tempDir = new File(getAppDirectory(), "temp");
            if (tempDir.exists()) {
                File[] tempFiles = tempDir.listFiles();
                if (tempFiles != null) {
                    for (File file : tempFiles) {
                        file.delete();
                    }
                }
            }
            Timber.i("تم تنظيف الملفات المؤقتة");
        } catch (Exception e) {
            Timber.e(e, "خطأ في تنظيف الملفات المؤقتة");
        }
    }
}
