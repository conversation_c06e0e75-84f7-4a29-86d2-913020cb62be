<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.mikrotik.cardgenerator" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/activity_main_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="242" endOffset="12"/></Target><Target id="@+id/card_user_manager" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="58" startOffset="12" endLine="115" endOffset="47"/></Target><Target id="@+id/card_hotspot" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="118" startOffset="12" endLine="174" endOffset="47"/></Target><Target id="@+id/btn_settings" view="Button"><Expressions/><location startLine="185" startOffset="12" endLine="192" endOffset="71"/></Target><Target id="@+id/btn_about" view="Button"><Expressions/><location startLine="194" startOffset="12" endLine="201" endOffset="71"/></Target><Target id="@+id/tv_app_version" view="TextView"><Expressions/><location startLine="228" startOffset="16" endLine="234" endOffset="63"/></Target></Targets></Layout>