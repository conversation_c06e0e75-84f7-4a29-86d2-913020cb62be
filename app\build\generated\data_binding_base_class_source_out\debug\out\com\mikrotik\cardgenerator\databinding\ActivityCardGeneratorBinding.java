// Generated by view binder compiler. Do not edit!
package com.mikrotik.cardgenerator.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.Spinner;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.tabs.TabLayout;
import com.google.android.material.textfield.TextInputEditText;
import com.mikrotik.cardgenerator.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityCardGeneratorBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btnConnect;

  @NonNull
  public final Button btnExportPdf;

  @NonNull
  public final Button btnGenerate;

  @NonNull
  public final Button btnSave;

  @NonNull
  public final Button btnSendToMikrotik;

  @NonNull
  public final CardView cardConnection;

  @NonNull
  public final CardView cardGeneration;

  @NonNull
  public final CardView cardPreview;

  @NonNull
  public final TextInputEditText editComment;

  @NonNull
  public final TextInputEditText editCount;

  @NonNull
  public final TextInputEditText editDays;

  @NonNull
  public final TextInputEditText editIpAddress;

  @NonNull
  public final TextInputEditText editLength;

  @NonNull
  public final TextInputEditText editLimitBytes;

  @NonNull
  public final TextInputEditText editLocation;

  @NonNull
  public final TextInputEditText editPassword;

  @NonNull
  public final TextInputEditText editPort;

  @NonNull
  public final TextInputEditText editPrefix;

  @NonNull
  public final TextInputEditText editPrice;

  @NonNull
  public final TextInputEditText editSuffix;

  @NonNull
  public final TextInputEditText editUsername;

  @NonNull
  public final LinearLayout layoutHotspotFields;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final RecyclerView recyclerCredentials;

  @NonNull
  public final Spinner spinnerCredentialType;

  @NonNull
  public final Spinner spinnerLimitUnit;

  @NonNull
  public final Spinner spinnerProfile;

  @NonNull
  public final TabLayout tabLayout;

  @NonNull
  public final TextView tvConnectionStatus;

  @NonNull
  public final TextView tvGeneratedCount;

  private ActivityCardGeneratorBinding(@NonNull LinearLayout rootView, @NonNull Button btnConnect,
      @NonNull Button btnExportPdf, @NonNull Button btnGenerate, @NonNull Button btnSave,
      @NonNull Button btnSendToMikrotik, @NonNull CardView cardConnection,
      @NonNull CardView cardGeneration, @NonNull CardView cardPreview,
      @NonNull TextInputEditText editComment, @NonNull TextInputEditText editCount,
      @NonNull TextInputEditText editDays, @NonNull TextInputEditText editIpAddress,
      @NonNull TextInputEditText editLength, @NonNull TextInputEditText editLimitBytes,
      @NonNull TextInputEditText editLocation, @NonNull TextInputEditText editPassword,
      @NonNull TextInputEditText editPort, @NonNull TextInputEditText editPrefix,
      @NonNull TextInputEditText editPrice, @NonNull TextInputEditText editSuffix,
      @NonNull TextInputEditText editUsername, @NonNull LinearLayout layoutHotspotFields,
      @NonNull ProgressBar progressBar, @NonNull RecyclerView recyclerCredentials,
      @NonNull Spinner spinnerCredentialType, @NonNull Spinner spinnerLimitUnit,
      @NonNull Spinner spinnerProfile, @NonNull TabLayout tabLayout,
      @NonNull TextView tvConnectionStatus, @NonNull TextView tvGeneratedCount) {
    this.rootView = rootView;
    this.btnConnect = btnConnect;
    this.btnExportPdf = btnExportPdf;
    this.btnGenerate = btnGenerate;
    this.btnSave = btnSave;
    this.btnSendToMikrotik = btnSendToMikrotik;
    this.cardConnection = cardConnection;
    this.cardGeneration = cardGeneration;
    this.cardPreview = cardPreview;
    this.editComment = editComment;
    this.editCount = editCount;
    this.editDays = editDays;
    this.editIpAddress = editIpAddress;
    this.editLength = editLength;
    this.editLimitBytes = editLimitBytes;
    this.editLocation = editLocation;
    this.editPassword = editPassword;
    this.editPort = editPort;
    this.editPrefix = editPrefix;
    this.editPrice = editPrice;
    this.editSuffix = editSuffix;
    this.editUsername = editUsername;
    this.layoutHotspotFields = layoutHotspotFields;
    this.progressBar = progressBar;
    this.recyclerCredentials = recyclerCredentials;
    this.spinnerCredentialType = spinnerCredentialType;
    this.spinnerLimitUnit = spinnerLimitUnit;
    this.spinnerProfile = spinnerProfile;
    this.tabLayout = tabLayout;
    this.tvConnectionStatus = tvConnectionStatus;
    this.tvGeneratedCount = tvGeneratedCount;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityCardGeneratorBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityCardGeneratorBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_card_generator, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityCardGeneratorBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_connect;
      Button btnConnect = ViewBindings.findChildViewById(rootView, id);
      if (btnConnect == null) {
        break missingId;
      }

      id = R.id.btn_export_pdf;
      Button btnExportPdf = ViewBindings.findChildViewById(rootView, id);
      if (btnExportPdf == null) {
        break missingId;
      }

      id = R.id.btn_generate;
      Button btnGenerate = ViewBindings.findChildViewById(rootView, id);
      if (btnGenerate == null) {
        break missingId;
      }

      id = R.id.btn_save;
      Button btnSave = ViewBindings.findChildViewById(rootView, id);
      if (btnSave == null) {
        break missingId;
      }

      id = R.id.btn_send_to_mikrotik;
      Button btnSendToMikrotik = ViewBindings.findChildViewById(rootView, id);
      if (btnSendToMikrotik == null) {
        break missingId;
      }

      id = R.id.card_connection;
      CardView cardConnection = ViewBindings.findChildViewById(rootView, id);
      if (cardConnection == null) {
        break missingId;
      }

      id = R.id.card_generation;
      CardView cardGeneration = ViewBindings.findChildViewById(rootView, id);
      if (cardGeneration == null) {
        break missingId;
      }

      id = R.id.card_preview;
      CardView cardPreview = ViewBindings.findChildViewById(rootView, id);
      if (cardPreview == null) {
        break missingId;
      }

      id = R.id.edit_comment;
      TextInputEditText editComment = ViewBindings.findChildViewById(rootView, id);
      if (editComment == null) {
        break missingId;
      }

      id = R.id.edit_count;
      TextInputEditText editCount = ViewBindings.findChildViewById(rootView, id);
      if (editCount == null) {
        break missingId;
      }

      id = R.id.edit_days;
      TextInputEditText editDays = ViewBindings.findChildViewById(rootView, id);
      if (editDays == null) {
        break missingId;
      }

      id = R.id.edit_ip_address;
      TextInputEditText editIpAddress = ViewBindings.findChildViewById(rootView, id);
      if (editIpAddress == null) {
        break missingId;
      }

      id = R.id.edit_length;
      TextInputEditText editLength = ViewBindings.findChildViewById(rootView, id);
      if (editLength == null) {
        break missingId;
      }

      id = R.id.edit_limit_bytes;
      TextInputEditText editLimitBytes = ViewBindings.findChildViewById(rootView, id);
      if (editLimitBytes == null) {
        break missingId;
      }

      id = R.id.edit_location;
      TextInputEditText editLocation = ViewBindings.findChildViewById(rootView, id);
      if (editLocation == null) {
        break missingId;
      }

      id = R.id.edit_password;
      TextInputEditText editPassword = ViewBindings.findChildViewById(rootView, id);
      if (editPassword == null) {
        break missingId;
      }

      id = R.id.edit_port;
      TextInputEditText editPort = ViewBindings.findChildViewById(rootView, id);
      if (editPort == null) {
        break missingId;
      }

      id = R.id.edit_prefix;
      TextInputEditText editPrefix = ViewBindings.findChildViewById(rootView, id);
      if (editPrefix == null) {
        break missingId;
      }

      id = R.id.edit_price;
      TextInputEditText editPrice = ViewBindings.findChildViewById(rootView, id);
      if (editPrice == null) {
        break missingId;
      }

      id = R.id.edit_suffix;
      TextInputEditText editSuffix = ViewBindings.findChildViewById(rootView, id);
      if (editSuffix == null) {
        break missingId;
      }

      id = R.id.edit_username;
      TextInputEditText editUsername = ViewBindings.findChildViewById(rootView, id);
      if (editUsername == null) {
        break missingId;
      }

      id = R.id.layout_hotspot_fields;
      LinearLayout layoutHotspotFields = ViewBindings.findChildViewById(rootView, id);
      if (layoutHotspotFields == null) {
        break missingId;
      }

      id = R.id.progress_bar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.recycler_credentials;
      RecyclerView recyclerCredentials = ViewBindings.findChildViewById(rootView, id);
      if (recyclerCredentials == null) {
        break missingId;
      }

      id = R.id.spinner_credential_type;
      Spinner spinnerCredentialType = ViewBindings.findChildViewById(rootView, id);
      if (spinnerCredentialType == null) {
        break missingId;
      }

      id = R.id.spinner_limit_unit;
      Spinner spinnerLimitUnit = ViewBindings.findChildViewById(rootView, id);
      if (spinnerLimitUnit == null) {
        break missingId;
      }

      id = R.id.spinner_profile;
      Spinner spinnerProfile = ViewBindings.findChildViewById(rootView, id);
      if (spinnerProfile == null) {
        break missingId;
      }

      id = R.id.tab_layout;
      TabLayout tabLayout = ViewBindings.findChildViewById(rootView, id);
      if (tabLayout == null) {
        break missingId;
      }

      id = R.id.tv_connection_status;
      TextView tvConnectionStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvConnectionStatus == null) {
        break missingId;
      }

      id = R.id.tv_generated_count;
      TextView tvGeneratedCount = ViewBindings.findChildViewById(rootView, id);
      if (tvGeneratedCount == null) {
        break missingId;
      }

      return new ActivityCardGeneratorBinding((LinearLayout) rootView, btnConnect, btnExportPdf,
          btnGenerate, btnSave, btnSendToMikrotik, cardConnection, cardGeneration, cardPreview,
          editComment, editCount, editDays, editIpAddress, editLength, editLimitBytes, editLocation,
          editPassword, editPort, editPrefix, editPrice, editSuffix, editUsername,
          layoutHotspotFields, progressBar, recyclerCredentials, spinnerCredentialType,
          spinnerLimitUnit, spinnerProfile, tabLayout, tvConnectionStatus, tvGeneratedCount);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
