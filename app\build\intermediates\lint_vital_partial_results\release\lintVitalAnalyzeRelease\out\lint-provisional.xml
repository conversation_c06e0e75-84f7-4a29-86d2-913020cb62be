<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.10.1" type="conditional_incidents">

    <incident
        id="DuplicatePlatformClasses"
        severity="fatal"
        message="`commons-logging` defines classes that conflict with classes now provided by Android. Solutions include finding newer versions or alternative libraries that don&apos;t have the same problem (for example, for `httpclient` use `HttpUrlConnection` or `okhttp` instead), or repackaging the library using something like `jarjar`.">
        <location
            file="${:app*projectDir}/build.gradle.kts"/>
    </incident>

</incidents>
