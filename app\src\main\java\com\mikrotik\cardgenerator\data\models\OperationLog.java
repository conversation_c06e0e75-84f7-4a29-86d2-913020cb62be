package com.mikrotik.cardgenerator.data.models;

import androidx.room.Entity;
import androidx.room.PrimaryKey;
import androidx.room.ColumnInfo;
import java.util.Date;

/**
 * نموذج بيانات سجل العمليات
 */
@Entity(tableName = "operation_log")
public class OperationLog {

    @PrimaryKey(autoGenerate = true)
    private int id;

    @ColumnInfo(name = "operation_type")
    private String operationType;

    @ColumnInfo(name = "description")
    private String description;

    @ColumnInfo(name = "status")
    private String status;

    @ColumnInfo(name = "timestamp")
    private Date timestamp;

    @ColumnInfo(name = "details")
    private String details;

    @ColumnInfo(name = "user_id")
    private String userId;

    @ColumnInfo(name = "system_type")
    private String systemType;

    // Constructors
    public OperationLog() {
        this.timestamp = new Date();
        this.status = "نجح";
    }

    @androidx.room.Ignore
    public OperationLog(String operationType, String description) {
        this();
        this.operationType = operationType;
        this.description = description;
    }

    @androidx.room.Ignore
    public OperationLog(String operationType, String description, String status) {
        this(operationType, description);
        this.status = status;
    }

    // Getters and Setters
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getOperationType() {
        return operationType;
    }

    public void setOperationType(String operationType) {
        this.operationType = operationType;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Date timestamp) {
        this.timestamp = timestamp;
    }

    public String getDetails() {
        return details;
    }

    public void setDetails(String details) {
        this.details = details;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getSystemType() {
        return systemType;
    }

    public void setSystemType(String systemType) {
        this.systemType = systemType;
    }

    @Override
    public String toString() {
        return "OperationLog{" +
                "id=" + id +
                ", operationType='" + operationType + '\'' +
                ", description='" + description + '\'' +
                ", status='" + status + '\'' +
                ", timestamp=" + timestamp +
                ", systemType='" + systemType + '\'' +
                '}';
    }
}
