package com.mikrotik.cardgenerator.data.database;

import androidx.annotation.NonNull;
import androidx.room.DatabaseConfiguration;
import androidx.room.InvalidationTracker;
import androidx.room.RoomDatabase;
import androidx.room.RoomOpenHelper;
import androidx.room.migration.AutoMigrationSpec;
import androidx.room.migration.Migration;
import androidx.room.util.DBUtil;
import androidx.room.util.TableInfo;
import androidx.sqlite.db.SupportSQLiteDatabase;
import androidx.sqlite.db.SupportSQLiteOpenHelper;
import java.lang.Class;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.annotation.processing.Generated;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class AppDatabase_Impl extends AppDatabase {
  private volatile UserManagerDao _userManagerDao;

  private volatile HotspotDao _hotspotDao;

  private volatile OperationLogDao _operationLogDao;

  @Override
  @NonNull
  protected SupportSQLiteOpenHelper createOpenHelper(@NonNull final DatabaseConfiguration config) {
    final SupportSQLiteOpenHelper.Callback _openCallback = new RoomOpenHelper(config, new RoomOpenHelper.Delegate(1) {
      @Override
      public void createAllTables(@NonNull final SupportSQLiteDatabase db) {
        db.execSQL("CREATE TABLE IF NOT EXISTS `user_manager_credentials` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `username` TEXT, `password` TEXT, `profile` TEXT, `comment` TEXT, `location` TEXT, `email` TEXT, `price` TEXT, `created_date` INTEGER, `updated_date` INTEGER, `is_active` INTEGER NOT NULL, `serial_number` INTEGER NOT NULL)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `hotspot_credentials` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `username` TEXT, `password` TEXT, `profile` TEXT, `comment` TEXT, `location` TEXT, `limit_bytes` TEXT, `limit_unit` TEXT, `days` TEXT, `email_template` TEXT, `price` TEXT, `created_date` INTEGER, `updated_date` INTEGER, `is_active` INTEGER NOT NULL, `serial_number` INTEGER NOT NULL, `uptime_limit` TEXT, `rate_limit` TEXT)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `operation_log` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `operation_type` TEXT, `description` TEXT, `status` TEXT, `timestamp` INTEGER, `details` TEXT, `user_id` TEXT, `system_type` TEXT)");
        db.execSQL("CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)");
        db.execSQL("INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, 'd2bb097bd746e1b5244cd4bad3b3a65f')");
      }

      @Override
      public void dropAllTables(@NonNull final SupportSQLiteDatabase db) {
        db.execSQL("DROP TABLE IF EXISTS `user_manager_credentials`");
        db.execSQL("DROP TABLE IF EXISTS `hotspot_credentials`");
        db.execSQL("DROP TABLE IF EXISTS `operation_log`");
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onDestructiveMigration(db);
          }
        }
      }

      @Override
      public void onCreate(@NonNull final SupportSQLiteDatabase db) {
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onCreate(db);
          }
        }
      }

      @Override
      public void onOpen(@NonNull final SupportSQLiteDatabase db) {
        mDatabase = db;
        internalInitInvalidationTracker(db);
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onOpen(db);
          }
        }
      }

      @Override
      public void onPreMigrate(@NonNull final SupportSQLiteDatabase db) {
        DBUtil.dropFtsSyncTriggers(db);
      }

      @Override
      public void onPostMigrate(@NonNull final SupportSQLiteDatabase db) {
      }

      @Override
      @NonNull
      public RoomOpenHelper.ValidationResult onValidateSchema(
          @NonNull final SupportSQLiteDatabase db) {
        final HashMap<String, TableInfo.Column> _columnsUserManagerCredentials = new HashMap<String, TableInfo.Column>(12);
        _columnsUserManagerCredentials.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserManagerCredentials.put("username", new TableInfo.Column("username", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserManagerCredentials.put("password", new TableInfo.Column("password", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserManagerCredentials.put("profile", new TableInfo.Column("profile", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserManagerCredentials.put("comment", new TableInfo.Column("comment", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserManagerCredentials.put("location", new TableInfo.Column("location", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserManagerCredentials.put("email", new TableInfo.Column("email", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserManagerCredentials.put("price", new TableInfo.Column("price", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserManagerCredentials.put("created_date", new TableInfo.Column("created_date", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserManagerCredentials.put("updated_date", new TableInfo.Column("updated_date", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserManagerCredentials.put("is_active", new TableInfo.Column("is_active", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserManagerCredentials.put("serial_number", new TableInfo.Column("serial_number", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysUserManagerCredentials = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesUserManagerCredentials = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoUserManagerCredentials = new TableInfo("user_manager_credentials", _columnsUserManagerCredentials, _foreignKeysUserManagerCredentials, _indicesUserManagerCredentials);
        final TableInfo _existingUserManagerCredentials = TableInfo.read(db, "user_manager_credentials");
        if (!_infoUserManagerCredentials.equals(_existingUserManagerCredentials)) {
          return new RoomOpenHelper.ValidationResult(false, "user_manager_credentials(com.mikrotik.cardgenerator.data.models.UserManagerCredential).\n"
                  + " Expected:\n" + _infoUserManagerCredentials + "\n"
                  + " Found:\n" + _existingUserManagerCredentials);
        }
        final HashMap<String, TableInfo.Column> _columnsHotspotCredentials = new HashMap<String, TableInfo.Column>(17);
        _columnsHotspotCredentials.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHotspotCredentials.put("username", new TableInfo.Column("username", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHotspotCredentials.put("password", new TableInfo.Column("password", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHotspotCredentials.put("profile", new TableInfo.Column("profile", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHotspotCredentials.put("comment", new TableInfo.Column("comment", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHotspotCredentials.put("location", new TableInfo.Column("location", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHotspotCredentials.put("limit_bytes", new TableInfo.Column("limit_bytes", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHotspotCredentials.put("limit_unit", new TableInfo.Column("limit_unit", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHotspotCredentials.put("days", new TableInfo.Column("days", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHotspotCredentials.put("email_template", new TableInfo.Column("email_template", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHotspotCredentials.put("price", new TableInfo.Column("price", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHotspotCredentials.put("created_date", new TableInfo.Column("created_date", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHotspotCredentials.put("updated_date", new TableInfo.Column("updated_date", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHotspotCredentials.put("is_active", new TableInfo.Column("is_active", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHotspotCredentials.put("serial_number", new TableInfo.Column("serial_number", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHotspotCredentials.put("uptime_limit", new TableInfo.Column("uptime_limit", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHotspotCredentials.put("rate_limit", new TableInfo.Column("rate_limit", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysHotspotCredentials = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesHotspotCredentials = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoHotspotCredentials = new TableInfo("hotspot_credentials", _columnsHotspotCredentials, _foreignKeysHotspotCredentials, _indicesHotspotCredentials);
        final TableInfo _existingHotspotCredentials = TableInfo.read(db, "hotspot_credentials");
        if (!_infoHotspotCredentials.equals(_existingHotspotCredentials)) {
          return new RoomOpenHelper.ValidationResult(false, "hotspot_credentials(com.mikrotik.cardgenerator.data.models.HotspotCredential).\n"
                  + " Expected:\n" + _infoHotspotCredentials + "\n"
                  + " Found:\n" + _existingHotspotCredentials);
        }
        final HashMap<String, TableInfo.Column> _columnsOperationLog = new HashMap<String, TableInfo.Column>(8);
        _columnsOperationLog.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOperationLog.put("operation_type", new TableInfo.Column("operation_type", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOperationLog.put("description", new TableInfo.Column("description", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOperationLog.put("status", new TableInfo.Column("status", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOperationLog.put("timestamp", new TableInfo.Column("timestamp", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOperationLog.put("details", new TableInfo.Column("details", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOperationLog.put("user_id", new TableInfo.Column("user_id", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOperationLog.put("system_type", new TableInfo.Column("system_type", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysOperationLog = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesOperationLog = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoOperationLog = new TableInfo("operation_log", _columnsOperationLog, _foreignKeysOperationLog, _indicesOperationLog);
        final TableInfo _existingOperationLog = TableInfo.read(db, "operation_log");
        if (!_infoOperationLog.equals(_existingOperationLog)) {
          return new RoomOpenHelper.ValidationResult(false, "operation_log(com.mikrotik.cardgenerator.data.models.OperationLog).\n"
                  + " Expected:\n" + _infoOperationLog + "\n"
                  + " Found:\n" + _existingOperationLog);
        }
        return new RoomOpenHelper.ValidationResult(true, null);
      }
    }, "d2bb097bd746e1b5244cd4bad3b3a65f", "6909a082aa7e77c5258f14b41d3de495");
    final SupportSQLiteOpenHelper.Configuration _sqliteConfig = SupportSQLiteOpenHelper.Configuration.builder(config.context).name(config.name).callback(_openCallback).build();
    final SupportSQLiteOpenHelper _helper = config.sqliteOpenHelperFactory.create(_sqliteConfig);
    return _helper;
  }

  @Override
  @NonNull
  protected InvalidationTracker createInvalidationTracker() {
    final HashMap<String, String> _shadowTablesMap = new HashMap<String, String>(0);
    final HashMap<String, Set<String>> _viewTables = new HashMap<String, Set<String>>(0);
    return new InvalidationTracker(this, _shadowTablesMap, _viewTables, "user_manager_credentials","hotspot_credentials","operation_log");
  }

  @Override
  public void clearAllTables() {
    super.assertNotMainThread();
    final SupportSQLiteDatabase _db = super.getOpenHelper().getWritableDatabase();
    try {
      super.beginTransaction();
      _db.execSQL("DELETE FROM `user_manager_credentials`");
      _db.execSQL("DELETE FROM `hotspot_credentials`");
      _db.execSQL("DELETE FROM `operation_log`");
      super.setTransactionSuccessful();
    } finally {
      super.endTransaction();
      _db.query("PRAGMA wal_checkpoint(FULL)").close();
      if (!_db.inTransaction()) {
        _db.execSQL("VACUUM");
      }
    }
  }

  @Override
  @NonNull
  protected Map<Class<?>, List<Class<?>>> getRequiredTypeConverters() {
    final HashMap<Class<?>, List<Class<?>>> _typeConvertersMap = new HashMap<Class<?>, List<Class<?>>>();
    _typeConvertersMap.put(UserManagerDao.class, UserManagerDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(HotspotDao.class, HotspotDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(OperationLogDao.class, OperationLogDao_Impl.getRequiredConverters());
    return _typeConvertersMap;
  }

  @Override
  @NonNull
  public Set<Class<? extends AutoMigrationSpec>> getRequiredAutoMigrationSpecs() {
    final HashSet<Class<? extends AutoMigrationSpec>> _autoMigrationSpecsSet = new HashSet<Class<? extends AutoMigrationSpec>>();
    return _autoMigrationSpecsSet;
  }

  @Override
  @NonNull
  public List<Migration> getAutoMigrations(
      @NonNull final Map<Class<? extends AutoMigrationSpec>, AutoMigrationSpec> autoMigrationSpecs) {
    final List<Migration> _autoMigrations = new ArrayList<Migration>();
    return _autoMigrations;
  }

  @Override
  public UserManagerDao userManagerDao() {
    if (_userManagerDao != null) {
      return _userManagerDao;
    } else {
      synchronized(this) {
        if(_userManagerDao == null) {
          _userManagerDao = new UserManagerDao_Impl(this);
        }
        return _userManagerDao;
      }
    }
  }

  @Override
  public HotspotDao hotspotDao() {
    if (_hotspotDao != null) {
      return _hotspotDao;
    } else {
      synchronized(this) {
        if(_hotspotDao == null) {
          _hotspotDao = new HotspotDao_Impl(this);
        }
        return _hotspotDao;
      }
    }
  }

  @Override
  public OperationLogDao operationLogDao() {
    if (_operationLogDao != null) {
      return _operationLogDao;
    } else {
      synchronized(this) {
        if(_operationLogDao == null) {
          _operationLogDao = new OperationLogDao_Impl(this);
        }
        return _operationLogDao;
      }
    }
  }
}
