package com.mikrotik.cardgenerator.utils;

import android.util.Log;
import timber.log.Timber;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * أدوات نظام السجلات
 */
public class LoggingUtils {
    
    private static final String LOG_FILE_NAME = "mikrotik_app.log";
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
    
    /**
     * شجرة السجلات للإصدار النهائي
     */
    public static class ReleaseTree extends Timber.Tree {
        
        @Override
        protected void log(int priority, String tag, String message, Throwable t) {
            if (priority == Log.VERBOSE || priority == Log.DEBUG) {
                return; // لا نسجل DEBUG و VERBOSE في الإصدار النهائي
            }
            
            // كتابة السجل في ملف
            writeToFile(priority, tag, message, t);
        }
        
        private void writeToFile(int priority, String tag, String message, Throwable t) {
            try {
                File logFile = new File(FileUtils.getLogsDirectory(), LOG_FILE_NAME);
                
                // إنشاء الملف إذا لم يكن موجوداً
                if (!logFile.exists()) {
                    logFile.createNewFile();
                }
                
                // كتابة السجل
                FileWriter writer = new FileWriter(logFile, true);
                String timestamp = DATE_FORMAT.format(new Date());
                String priorityString = getPriorityString(priority);
                
                writer.append(String.format("[%s] %s/%s: %s\n", 
                    timestamp, priorityString, tag, message));
                
                if (t != null) {
                    writer.append("Exception: " + t.getMessage() + "\n");
                }
                
                writer.close();
                
            } catch (IOException e) {
                Log.e("LoggingUtils", "خطأ في كتابة السجل", e);
            }
        }
        
        private String getPriorityString(int priority) {
            switch (priority) {
                case Log.ERROR: return "ERROR";
                case Log.WARN: return "WARN";
                case Log.INFO: return "INFO";
                case Log.DEBUG: return "DEBUG";
                case Log.VERBOSE: return "VERBOSE";
                default: return "UNKNOWN";
            }
        }
    }
    
    /**
     * تنظيف ملفات السجلات القديمة
     */
    public static void cleanOldLogs(int daysToKeep) {
        try {
            File logsDir = FileUtils.getLogsDirectory();
            File[] logFiles = logsDir.listFiles();
            
            if (logFiles != null) {
                long cutoffTime = System.currentTimeMillis() - (daysToKeep * 24 * 60 * 60 * 1000L);
                
                for (File file : logFiles) {
                    if (file.lastModified() < cutoffTime) {
                        file.delete();
                        Timber.i("تم حذف ملف السجل القديم: " + file.getName());
                    }
                }
            }
        } catch (Exception e) {
            Timber.e(e, "خطأ في تنظيف ملفات السجلات القديمة");
        }
    }
    
    /**
     * الحصول على حجم ملفات السجلات
     */
    public static long getLogFilesSize() {
        try {
            File logsDir = FileUtils.getLogsDirectory();
            File[] logFiles = logsDir.listFiles();
            long totalSize = 0;
            
            if (logFiles != null) {
                for (File file : logFiles) {
                    totalSize += file.length();
                }
            }
            
            return totalSize;
        } catch (Exception e) {
            Timber.e(e, "خطأ في حساب حجم ملفات السجلات");
            return 0;
        }
    }
    
    /**
     * تصدير ملفات السجلات
     */
    public static File exportLogs() {
        try {
            File logsDir = FileUtils.getLogsDirectory();
            File exportFile = new File(FileUtils.getExportsDirectory(), 
                "logs_export_" + System.currentTimeMillis() + ".txt");
            
            FileWriter writer = new FileWriter(exportFile);
            File[] logFiles = logsDir.listFiles();
            
            if (logFiles != null) {
                for (File logFile : logFiles) {
                    writer.append("=== " + logFile.getName() + " ===\n");
                    String content = FileUtils.readFileContent(logFile);
                    writer.append(content);
                    writer.append("\n\n");
                }
            }
            
            writer.close();
            Timber.i("تم تصدير السجلات إلى: " + exportFile.getAbsolutePath());
            return exportFile;
            
        } catch (Exception e) {
            Timber.e(e, "خطأ في تصدير السجلات");
            return null;
        }
    }
}
