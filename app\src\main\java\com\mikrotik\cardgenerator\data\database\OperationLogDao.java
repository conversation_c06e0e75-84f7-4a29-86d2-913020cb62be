package com.mikrotik.cardgenerator.data.database;

import androidx.lifecycle.LiveData;
import androidx.room.*;
import com.mikrotik.cardgenerator.data.models.OperationLog;
import java.util.List;

/**
 * DAO لسجل العمليات
 */
@Dao
public interface OperationLogDao {
    
    @Query("SELECT * FROM operation_log ORDER BY timestamp DESC")
    LiveData<List<OperationLog>> getAllLogs();
    
    @Query("SELECT * FROM operation_log ORDER BY timestamp DESC LIMIT :limit")
    LiveData<List<OperationLog>> getRecentLogs(int limit);
    
    @Query("SELECT * FROM operation_log WHERE id = :id")
    LiveData<OperationLog> getLogById(int id);
    
    @Query("SELECT * FROM operation_log WHERE operation_type = :operationType ORDER BY timestamp DESC")
    LiveData<List<OperationLog>> getLogsByOperationType(String operationType);
    
    @Query("SELECT * FROM operation_log WHERE status = :status ORDER BY timestamp DESC")
    LiveData<List<OperationLog>> getLogsByStatus(String status);
    
    @Query("SELECT * FROM operation_log WHERE system_type = :systemType ORDER BY timestamp DESC")
    LiveData<List<OperationLog>> getLogsBySystemType(String systemType);
    
    @Query("SELECT * FROM operation_log WHERE timestamp BETWEEN :startDate AND :endDate ORDER BY timestamp DESC")
    LiveData<List<OperationLog>> getLogsByDateRange(long startDate, long endDate);
    
    @Query("SELECT * FROM operation_log WHERE description LIKE :searchQuery OR details LIKE :searchQuery ORDER BY timestamp DESC")
    LiveData<List<OperationLog>> searchLogs(String searchQuery);
    
    @Query("SELECT COUNT(*) FROM operation_log")
    LiveData<Integer> getLogsCount();
    
    @Query("SELECT COUNT(*) FROM operation_log WHERE status = 'نجح'")
    LiveData<Integer> getSuccessLogsCount();
    
    @Query("SELECT COUNT(*) FROM operation_log WHERE status = 'فشل'")
    LiveData<Integer> getFailureLogsCount();
    
    @Query("SELECT DISTINCT operation_type FROM operation_log ORDER BY operation_type")
    LiveData<List<String>> getAllOperationTypes();
    
    @Insert
    long insertLog(OperationLog log);
    
    @Insert
    List<Long> insertLogs(List<OperationLog> logs);
    
    @Update
    void updateLog(OperationLog log);
    
    @Delete
    void deleteLog(OperationLog log);
    
    @Query("DELETE FROM operation_log WHERE id = :id")
    void deleteLogById(int id);
    
    @Query("DELETE FROM operation_log")
    void deleteAllLogs();
    
    @Query("DELETE FROM operation_log WHERE timestamp < :beforeDate")
    void deleteLogsBeforeDate(long beforeDate);
    
    @Query("DELETE FROM operation_log WHERE status = 'فشل'")
    void deleteFailureLogs();
}
