package com.mikrotik.cardgenerator.data.database;

import androidx.room.Database;
import androidx.room.Room;
import androidx.room.RoomDatabase;
import androidx.room.TypeConverters;
import androidx.room.migration.Migration;
import androidx.sqlite.db.SupportSQLiteDatabase;
import android.content.Context;

import com.mikrotik.cardgenerator.data.models.UserManagerCredential;
import com.mikrotik.cardgenerator.data.models.HotspotCredential;
import com.mikrotik.cardgenerator.data.models.OperationLog;

/**
 * قاعدة البيانات الرئيسية للتطبيق
 */
@Database(
    entities = {
        UserManagerCredential.class,
        HotspotCredential.class,
        OperationLog.class
    },
    version = 1,
    exportSchema = false
)
@TypeConverters({Converters.class})
public abstract class AppDatabase extends RoomDatabase {
    
    private static volatile AppDatabase INSTANCE;
    
    public abstract UserManagerDao userManagerDao();
    public abstract HotspotDao hotspotDao();
    public abstract OperationLogDao operationLogDao();
    
    public static AppDatabase getDatabase(final Context context) {
        if (INSTANCE == null) {
            synchronized (AppDatabase.class) {
                if (INSTANCE == null) {
                    INSTANCE = Room.databaseBuilder(
                            context.getApplicationContext(),
                            AppDatabase.class,
                            "mikrotik_cards_database"
                    )
                    .fallbackToDestructiveMigration()
                    .addCallback(roomDatabaseCallback)
                    .build();
                }
            }
        }
        return INSTANCE;
    }
    
    /**
     * Callback لإعداد قاعدة البيانات عند الإنشاء
     */
    private static RoomDatabase.Callback roomDatabaseCallback = new RoomDatabase.Callback() {
        @Override
        public void onCreate(SupportSQLiteDatabase db) {
            super.onCreate(db);
            // يمكن إضافة بيانات افتراضية هنا
        }
        
        @Override
        public void onOpen(SupportSQLiteDatabase db) {
            super.onOpen(db);
            // يمكن إضافة عمليات عند فتح قاعدة البيانات
        }
    };
    
    /**
     * Migration للإصدارات المستقبلية
     */
    static final Migration MIGRATION_1_2 = new Migration(1, 2) {
        @Override
        public void migrate(SupportSQLiteDatabase database) {
            // إضافة أعمدة جديدة أو تعديل الجداول
        }
    };
}
