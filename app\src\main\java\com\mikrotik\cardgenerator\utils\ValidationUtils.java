package com.mikrotik.cardgenerator.utils;

import android.util.Patterns;
import java.util.regex.Pattern;

/**
 * أدوات التحقق من صحة البيانات
 */
public class ValidationUtils {
    
    // أنماط التحقق
    private static final Pattern IP_PATTERN = Pattern.compile(
        "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$"
    );
    
    private static final Pattern USERNAME_PATTERN = Pattern.compile("^[a-zA-Z0-9_-]{3,20}$");
    private static final Pattern PASSWORD_PATTERN = Pattern.compile("^.{4,50}$");
    
    /**
     * التحقق من صحة عنوان IP
     */
    public static boolean isValidIpAddress(String ip) {
        if (ip == null || ip.trim().isEmpty()) {
            return false;
        }
        return IP_PATTERN.matcher(ip.trim()).matches();
    }
    
    /**
     * التحقق من صحة رقم المنفذ
     */
    public static boolean isValidPort(String port) {
        try {
            int portNum = Integer.parseInt(port.trim());
            return portNum >= 1 && portNum <= 65535;
        } catch (NumberFormatException e) {
            return false;
        }
    }
    
    /**
     * التحقق من صحة رقم المنفذ
     */
    public static boolean isValidPort(int port) {
        return port >= 1 && port <= 65535;
    }
    
    /**
     * التحقق من صحة البريد الإلكتروني
     */
    public static boolean isValidEmail(String email) {
        if (email == null || email.trim().isEmpty()) {
            return true; // البريد الإلكتروني اختياري
        }
        return Patterns.EMAIL_ADDRESS.matcher(email.trim()).matches();
    }
    
    /**
     * التحقق من صحة اسم المستخدم
     */
    public static boolean isValidUsername(String username) {
        if (username == null || username.trim().isEmpty()) {
            return false;
        }
        return USERNAME_PATTERN.matcher(username.trim()).matches();
    }
    
    /**
     * التحقق من صحة كلمة المرور
     */
    public static boolean isValidPassword(String password) {
        if (password == null) {
            return false;
        }
        return PASSWORD_PATTERN.matcher(password).matches();
    }
    
    /**
     * التحقق من صحة الرقم
     */
    public static boolean isValidNumber(String number) {
        try {
            Integer.parseInt(number.trim());
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }
    
    /**
     * التحقق من صحة الرقم في نطاق معين
     */
    public static boolean isValidNumberInRange(String number, int min, int max) {
        try {
            int num = Integer.parseInt(number.trim());
            return num >= min && num <= max;
        } catch (NumberFormatException e) {
            return false;
        }
    }
    
    /**
     * التحقق من صحة الرقم العشري
     */
    public static boolean isValidDecimal(String decimal) {
        try {
            Double.parseDouble(decimal.trim());
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }
    
    /**
     * التحقق من أن النص ليس فارغاً
     */
    public static boolean isNotEmpty(String text) {
        return text != null && !text.trim().isEmpty();
    }
    
    /**
     * التحقق من طول النص
     */
    public static boolean isValidLength(String text, int minLength, int maxLength) {
        if (text == null) {
            return false;
        }
        int length = text.trim().length();
        return length >= minLength && length <= maxLength;
    }
    
    /**
     * التحقق من صحة اسم الملف
     */
    public static boolean isValidFileName(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            return false;
        }
        
        // التحقق من الأحرف المحظورة في أسماء الملفات
        String invalidChars = "<>:\"/\\|?*";
        for (char c : invalidChars.toCharArray()) {
            if (fileName.contains(String.valueOf(c))) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * التحقق من صحة امتداد الملف
     */
    public static boolean isValidFileExtension(String fileName, String[] allowedExtensions) {
        if (fileName == null || fileName.trim().isEmpty()) {
            return false;
        }
        
        String extension = getFileExtension(fileName);
        if (extension == null) {
            return false;
        }
        
        for (String allowedExt : allowedExtensions) {
            if (extension.equalsIgnoreCase(allowedExt)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * الحصول على امتداد الملف
     */
    public static String getFileExtension(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            return null;
        }
        
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == fileName.length() - 1) {
            return null;
        }
        
        return fileName.substring(lastDotIndex + 1);
    }
    
    /**
     * تنظيف النص من الأحرف الخاصة
     */
    public static String sanitizeText(String text) {
        if (text == null) {
            return "";
        }
        
        // إزالة الأحرف الخاصة والمسافات الزائدة
        return text.trim().replaceAll("[<>\"'&]", "");
    }
    
    /**
     * التحقق من صحة الـ MAC Address
     */
    public static boolean isValidMacAddress(String macAddress) {
        if (macAddress == null || macAddress.trim().isEmpty()) {
            return false;
        }
        
        Pattern macPattern = Pattern.compile("^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$");
        return macPattern.matcher(macAddress.trim()).matches();
    }
}
