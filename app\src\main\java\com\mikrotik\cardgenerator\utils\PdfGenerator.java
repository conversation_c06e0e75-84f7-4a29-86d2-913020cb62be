package com.mikrotik.cardgenerator.utils;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Image;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.element.Cell;
import com.itextpdf.layout.properties.TextAlignment;
import com.itextpdf.layout.properties.UnitValue;
import com.itextpdf.io.image.ImageDataFactory;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.colors.ColorConstants;
import com.mikrotik.cardgenerator.data.models.UserManagerCredential;
import com.mikrotik.cardgenerator.data.models.HotspotCredential;
import timber.log.Timber;

import java.io.File;
import java.io.FileOutputStream;
import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * مولد ملفات PDF للكروت
 */
public class PdfGenerator {

    private Context context;
    private PdfFont arabicFont;

    public PdfGenerator(Context context) {
        this.context = context;
        initializeFont();
    }

    /**
     * إعداد الخط العربي
     */
    private void initializeFont() {
        try {
            // استخدام خط افتراضي يدعم العربية
            arabicFont = PdfFontFactory.createFont();
        } catch (Exception e) {
            Timber.e(e, "خطأ في إعداد الخط العربي");
        }
    }

    /**
     * إنشاء PDF لحسابات User Manager
     */
    public File generateUserManagerPdf(List<UserManagerCredential> credentials,
                                     PdfSettings settings) {
        try {
            String fileName = FileUtils.generateUniqueFileName("user_manager_cards", "pdf");
            File pdfFile = new File(FileUtils.getExportsDirectory(), fileName);

            PdfWriter writer = new PdfWriter(new FileOutputStream(pdfFile));
            PdfDocument pdfDoc = new PdfDocument(writer);
            Document document = new Document(pdfDoc);

            // إضافة العنوان
            Paragraph title = new Paragraph("كروت User Manager")
                    .setFont(arabicFont)
                    .setFontSize(18)
                    .setTextAlignment(TextAlignment.CENTER)
                    .setMarginBottom(20);
            document.add(title);

            // إنشاء جدول الكروت
            Table table = new Table(UnitValue.createPercentArray(settings.getColumns()))
                    .setWidth(UnitValue.createPercentValue(100));

            int cardCount = 0;
            for (UserManagerCredential credential : credentials) {
                Cell cardCell = createUserManagerCard(credential, settings);
                table.addCell(cardCell);
                cardCount++;

                // إضافة صف جديد عند الحاجة
                if (cardCount % settings.getColumns() == 0) {
                    // يمكن إضافة منطق إضافي هنا
                }
            }

            document.add(table);
            document.close();

            Timber.i("تم إنشاء PDF بنجاح: " + pdfFile.getAbsolutePath());
            return pdfFile;

        } catch (Exception e) {
            Timber.e(e, "خطأ في إنشاء PDF لـ User Manager");
            return null;
        }
    }

    /**
     * إنشاء PDF لحسابات Hotspot
     */
    public File generateHotspotPdf(List<HotspotCredential> credentials,
                                 PdfSettings settings) {
        try {
            String fileName = FileUtils.generateUniqueFileName("hotspot_cards", "pdf");
            File pdfFile = new File(FileUtils.getExportsDirectory(), fileName);

            PdfWriter writer = new PdfWriter(new FileOutputStream(pdfFile));
            PdfDocument pdfDoc = new PdfDocument(writer);
            Document document = new Document(pdfDoc);

            // إضافة العنوان
            Paragraph title = new Paragraph("كروت Hotspot")
                    .setFont(arabicFont)
                    .setFontSize(18)
                    .setTextAlignment(TextAlignment.CENTER)
                    .setMarginBottom(20);
            document.add(title);

            // إنشاء جدول الكروت
            Table table = new Table(UnitValue.createPercentArray(settings.getColumns()))
                    .setWidth(UnitValue.createPercentValue(100));

            for (HotspotCredential credential : credentials) {
                Cell cardCell = createHotspotCard(credential, settings);
                table.addCell(cardCell);
            }

            document.add(table);
            document.close();

            Timber.i("تم إنشاء PDF بنجاح: " + pdfFile.getAbsolutePath());
            return pdfFile;

        } catch (Exception e) {
            Timber.e(e, "خطأ في إنشاء PDF لـ Hotspot");
            return null;
        }
    }

    /**
     * إنشاء كارت User Manager
     */
    private Cell createUserManagerCard(UserManagerCredential credential, PdfSettings settings) {
        Cell cell = new Cell();
        cell.setPadding(10);
        cell.setBorder(new com.itextpdf.layout.borders.SolidBorder(1));

        // إضافة صورة خلفية إذا كانت متوفرة
        if (settings.getBackgroundImage() != null) {
            try {
                Image bgImage = new Image(ImageDataFactory.create(settings.getBackgroundImage()));
                bgImage.setWidth(UnitValue.createPercentValue(100));
                cell.add(bgImage);
            } catch (Exception e) {
                Timber.w("خطأ في إضافة صورة الخلفية");
            }
        }

        // إضافة اسم المستخدم
        Paragraph username = new Paragraph("المستخدم: " + credential.getUsername())
                .setFont(arabicFont)
                .setFontSize(12)
                .setBold();
        cell.add(username);

        // إضافة كلمة المرور
        Paragraph password = new Paragraph("كلمة المرور: " + credential.getPassword())
                .setFont(arabicFont)
                .setFontSize(10);
        cell.add(password);

        // إضافة البروفايل
        if (credential.getProfile() != null && !credential.getProfile().isEmpty()) {
            Paragraph profile = new Paragraph("البروفايل: " + credential.getProfile())
                    .setFont(arabicFont)
                    .setFontSize(9);
            cell.add(profile);
        }

        // إضافة السعر
        if (credential.getPrice() != null && !credential.getPrice().isEmpty()) {
            Paragraph price = new Paragraph("السعر: " + credential.getPrice())
                    .setFont(arabicFont)
                    .setFontSize(9)
                    .setFontColor(ColorConstants.BLUE);
            cell.add(price);
        }

        // إضافة QR Code
        if (settings.isIncludeQrCode()) {
            try {
                String qrData = "Username: " + credential.getUsername() +
                               "\nPassword: " + credential.getPassword();
                Bitmap qrBitmap = QrCodeGenerator.generateQrCode(qrData, 100, 100);

                ByteArrayOutputStream stream = new ByteArrayOutputStream();
                qrBitmap.compress(Bitmap.CompressFormat.PNG, 100, stream);
                byte[] qrBytes = stream.toByteArray();

                Image qrImage = new Image(ImageDataFactory.create(qrBytes));
                qrImage.setWidth(50);
                qrImage.setHeight(50);
                cell.add(qrImage);

            } catch (Exception e) {
                Timber.w("خطأ في إضافة QR Code");
            }
        }

        return cell;
    }

    /**
     * إنشاء كارت Hotspot
     */
    private Cell createHotspotCard(HotspotCredential credential, PdfSettings settings) {
        Cell cell = new Cell();
        cell.setPadding(10);
        cell.setBorder(new com.itextpdf.layout.borders.SolidBorder(1));

        // إضافة صورة خلفية إذا كانت متوفرة
        if (settings.getBackgroundImage() != null) {
            try {
                Image bgImage = new Image(ImageDataFactory.create(settings.getBackgroundImage()));
                bgImage.setWidth(UnitValue.createPercentValue(100));
                cell.add(bgImage);
            } catch (Exception e) {
                Timber.w("خطأ في إضافة صورة الخلفية");
            }
        }

        // إضافة اسم المستخدم
        Paragraph username = new Paragraph("المستخدم: " + credential.getUsername())
                .setFont(arabicFont)
                .setFontSize(12)
                .setBold();
        cell.add(username);

        // إضافة كلمة المرور
        Paragraph password = new Paragraph("كلمة المرور: " + credential.getPassword())
                .setFont(arabicFont)
                .setFontSize(10);
        cell.add(password);

        // إضافة البروفايل
        if (credential.getProfile() != null && !credential.getProfile().isEmpty()) {
            Paragraph profile = new Paragraph("البروفايل: " + credential.getProfile())
                    .setFont(arabicFont)
                    .setFontSize(9);
            cell.add(profile);
        }

        // إضافة حد البيانات
        if (credential.getLimitBytes() != null && !credential.getLimitBytes().isEmpty()) {
            String limitText = "الحد: " + credential.getLimitBytes();
            if (credential.getLimitUnit() != null) {
                limitText += " " + credential.getLimitUnit();
            }
            Paragraph limit = new Paragraph(limitText)
                    .setFont(arabicFont)
                    .setFontSize(9);
            cell.add(limit);
        }

        // إضافة عدد الأيام
        if (credential.getDays() != null && !credential.getDays().isEmpty()) {
            Paragraph days = new Paragraph("المدة: " + credential.getDays() + " يوم")
                    .setFont(arabicFont)
                    .setFontSize(9);
            cell.add(days);
        }

        // إضافة السعر
        if (credential.getPrice() != null && !credential.getPrice().isEmpty()) {
            Paragraph price = new Paragraph("السعر: " + credential.getPrice())
                    .setFont(arabicFont)
                    .setFontSize(9)
                    .setFontColor(ColorConstants.BLUE);
            cell.add(price);
        }

        return cell;
    }

    /**
     * إعدادات PDF
     */
    public static class PdfSettings {
        private int columns = 2;
        private int rows = 5;
        private String backgroundImage;
        private boolean includeQrCode = true;
        private float cardWidth = 200;
        private float cardHeight = 120;

        // Getters and Setters
        public int getColumns() { return columns; }
        public void setColumns(int columns) { this.columns = columns; }

        public int getRows() { return rows; }
        public void setRows(int rows) { this.rows = rows; }

        public String getBackgroundImage() { return backgroundImage; }
        public void setBackgroundImage(String backgroundImage) { this.backgroundImage = backgroundImage; }

        public boolean isIncludeQrCode() { return includeQrCode; }
        public void setIncludeQrCode(boolean includeQrCode) { this.includeQrCode = includeQrCode; }

        public float getCardWidth() { return cardWidth; }
        public void setCardWidth(float cardWidth) { this.cardWidth = cardWidth; }

        public float getCardHeight() { return cardHeight; }
        public void setCardHeight(float cardHeight) { this.cardHeight = cardHeight; }
    }
}
