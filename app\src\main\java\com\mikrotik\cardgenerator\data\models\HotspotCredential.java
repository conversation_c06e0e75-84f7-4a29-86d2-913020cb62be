package com.mikrotik.cardgenerator.data.models;

import androidx.room.Entity;
import androidx.room.PrimaryKey;
import androidx.room.ColumnInfo;
import java.util.Date;

/**
 * نموذج بيانات حسابات Hotspot
 */
@Entity(tableName = "hotspot_credentials")
public class HotspotCredential {

    @PrimaryKey(autoGenerate = true)
    private int id;

    @ColumnInfo(name = "username")
    private String username;

    @ColumnInfo(name = "password")
    private String password;

    @ColumnInfo(name = "profile")
    private String profile;

    @ColumnInfo(name = "comment")
    private String comment;

    @ColumnInfo(name = "location")
    private String location;

    @ColumnInfo(name = "limit_bytes")
    private String limitBytes;

    @ColumnInfo(name = "limit_unit")
    private String limitUnit;

    @ColumnInfo(name = "days")
    private String days;

    @ColumnInfo(name = "email_template")
    private String emailTemplate;

    @ColumnInfo(name = "price")
    private String price;

    @ColumnInfo(name = "created_date")
    private Date createdDate;

    @ColumnInfo(name = "updated_date")
    private Date updatedDate;

    @ColumnInfo(name = "is_active")
    private boolean isActive;

    @ColumnInfo(name = "serial_number")
    private int serialNumber;

    @ColumnInfo(name = "uptime_limit")
    private String uptimeLimit;

    @ColumnInfo(name = "rate_limit")
    private String rateLimit;

    // Constructors
    public HotspotCredential() {
        this.createdDate = new Date();
        this.updatedDate = new Date();
        this.isActive = true;
    }

    @androidx.room.Ignore
    public HotspotCredential(String username, String password, String profile) {
        this();
        this.username = username;
        this.password = password;
        this.profile = profile;
    }

    // Getters and Setters
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getProfile() {
        return profile;
    }

    public void setProfile(String profile) {
        this.profile = profile;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getLimitBytes() {
        return limitBytes;
    }

    public void setLimitBytes(String limitBytes) {
        this.limitBytes = limitBytes;
    }

    public String getLimitUnit() {
        return limitUnit;
    }

    public void setLimitUnit(String limitUnit) {
        this.limitUnit = limitUnit;
    }

    public String getDays() {
        return days;
    }

    public void setDays(String days) {
        this.days = days;
    }

    public String getEmailTemplate() {
        return emailTemplate;
    }

    public void setEmailTemplate(String emailTemplate) {
        this.emailTemplate = emailTemplate;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public Date getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(Date updatedDate) {
        this.updatedDate = updatedDate;
    }

    public boolean isActive() {
        return isActive;
    }

    public void setActive(boolean active) {
        isActive = active;
    }

    public int getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(int serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getUptimeLimit() {
        return uptimeLimit;
    }

    public void setUptimeLimit(String uptimeLimit) {
        this.uptimeLimit = uptimeLimit;
    }

    public String getRateLimit() {
        return rateLimit;
    }

    public void setRateLimit(String rateLimit) {
        this.rateLimit = rateLimit;
    }

    @Override
    public String toString() {
        return "HotspotCredential{" +
                "id=" + id +
                ", username='" + username + '\'' +
                ", profile='" + profile + '\'' +
                ", comment='" + comment + '\'' +
                ", location='" + location + '\'' +
                ", limitBytes='" + limitBytes + '\'' +
                ", limitUnit='" + limitUnit + '\'' +
                ", days='" + days + '\'' +
                ", price='" + price + '\'' +
                ", serialNumber=" + serialNumber +
                ", isActive=" + isActive +
                '}';
    }
}
