package com.mikrotik.cardgenerator.ui;

import android.content.Intent;
import android.os.Bundle;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.*;
import androidx.appcompat.app.AppCompatActivity;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import com.google.android.material.tabs.TabLayout;
import com.mikrotik.cardgenerator.R;
import com.mikrotik.cardgenerator.MikroTikApplication;
import com.mikrotik.cardgenerator.data.models.UserManagerCredential;
import com.mikrotik.cardgenerator.data.models.HotspotCredential;
import com.mikrotik.cardgenerator.utils.ValidationUtils;
import com.mikrotik.cardgenerator.utils.EncryptionUtils;
import timber.log.Timber;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * شاشة توليد الكروت
 */
public class CardGeneratorActivity extends AppCompatActivity {

    private String systemType;
    private MikroTikApplication app;
    private EncryptionUtils encryptionUtils;

    // مكونات واجهة المستخدم
    private TabLayout tabLayout;
    private CardView connectionCard;
    private CardView generationCard;
    private CardView previewCard;

    // حقول الاتصال
    private TextInputEditText ipAddressEdit;
    private TextInputEditText portEdit;
    private TextInputEditText usernameEdit;
    private TextInputEditText passwordEdit;
    private Button connectButton;
    private TextView connectionStatusText;

    // حقول التوليد
    private TextInputEditText prefixEdit;
    private TextInputEditText suffixEdit;
    private TextInputEditText countEdit;
    private TextInputEditText lengthEdit;
    private Spinner credentialTypeSpinner;
    private Spinner profileSpinner;
    private TextInputEditText commentEdit;
    private TextInputEditText locationEdit;
    private TextInputEditText priceEdit;

    // حقول خاصة بـ Hotspot
    private TextInputEditText limitBytesEdit;
    private Spinner limitUnitSpinner;
    private TextInputEditText daysEdit;

    // أزرار العمليات
    private Button generateButton;
    private Button saveButton;
    private Button exportPdfButton;
    private Button sendToMikrotikButton;

    // معاينة النتائج
    private RecyclerView credentialsRecyclerView;
    private TextView generatedCountText;
    private ProgressBar progressBar;

    // البيانات المولدة
    private List<UserManagerCredential> userManagerCredentials;
    private List<HotspotCredential> hotspotCredentials;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_card_generator);

        // الحصول على نوع النظام
        systemType = getIntent().getStringExtra("system_type");
        if (systemType == null) {
            systemType = "user_manager"; // افتراضي
        }

        app = MikroTikApplication.getInstance();
        encryptionUtils = app.getEncryptionUtils();

        initializeViews();
        setupUI();
        setupClickListeners();
        loadSettings();

        Timber.i("تم فتح شاشة توليد الكروت - النظام: " + systemType);
    }

    /**
     * إعداد المكونات
     */
    private void initializeViews() {
        // التبويبات
        tabLayout = findViewById(R.id.tab_layout);

        // الكروت الرئيسية
        connectionCard = findViewById(R.id.card_connection);
        generationCard = findViewById(R.id.card_generation);
        previewCard = findViewById(R.id.card_preview);

        // حقول الاتصال
        ipAddressEdit = findViewById(R.id.edit_ip_address);
        portEdit = findViewById(R.id.edit_port);
        usernameEdit = findViewById(R.id.edit_username);
        passwordEdit = findViewById(R.id.edit_password);
        connectButton = findViewById(R.id.btn_connect);
        connectionStatusText = findViewById(R.id.tv_connection_status);

        // حقول التوليد
        prefixEdit = findViewById(R.id.edit_prefix);
        suffixEdit = findViewById(R.id.edit_suffix);
        countEdit = findViewById(R.id.edit_count);
        lengthEdit = findViewById(R.id.edit_length);
        credentialTypeSpinner = findViewById(R.id.spinner_credential_type);
        profileSpinner = findViewById(R.id.spinner_profile);
        commentEdit = findViewById(R.id.edit_comment);
        locationEdit = findViewById(R.id.edit_location);
        priceEdit = findViewById(R.id.edit_price);

        // حقول Hotspot
        limitBytesEdit = findViewById(R.id.edit_limit_bytes);
        limitUnitSpinner = findViewById(R.id.spinner_limit_unit);
        daysEdit = findViewById(R.id.edit_days);

        // أزرار العمليات
        generateButton = findViewById(R.id.btn_generate);
        saveButton = findViewById(R.id.btn_save);
        exportPdfButton = findViewById(R.id.btn_export_pdf);
        sendToMikrotikButton = findViewById(R.id.btn_send_to_mikrotik);

        // معاينة النتائج
        credentialsRecyclerView = findViewById(R.id.recycler_credentials);
        generatedCountText = findViewById(R.id.tv_generated_count);
        progressBar = findViewById(R.id.progress_bar);

        // إعداد RecyclerView
        credentialsRecyclerView.setLayoutManager(new LinearLayoutManager(this));

        // تهيئة القوائم
        userManagerCredentials = new ArrayList<>();
        hotspotCredentials = new ArrayList<>();
    }

    /**
     * إعداد واجهة المستخدم
     */
    private void setupUI() {
        // إعداد العنوان
        String title = systemType.equals("user_manager") ?
                "توليد كروت User Manager" : "توليد كروت Hotspot";
        if (getSupportActionBar() != null) {
            getSupportActionBar().setTitle(title);
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        }

        // إعداد التبويبات
        tabLayout.addTab(tabLayout.newTab().setText("🔗 الاتصال"));
        tabLayout.addTab(tabLayout.newTab().setText("⚙️ التوليد"));
        tabLayout.addTab(tabLayout.newTab().setText("👁️ المعاينة"));

        // إعداد Spinners
        setupSpinners();

        // إخفاء/إظهار حقول حسب نوع النظام
        setupFieldsVisibility();

        // إعداد القيم الافتراضية
        setupDefaultValues();
    }

    /**
     * إعداد القوائم المنسدلة
     */
    private void setupSpinners() {
        // قائمة أنواع البيانات
        String[] credentialTypes = {"أرقام", "حروف", "مختلط"};
        ArrayAdapter<String> credentialAdapter = new ArrayAdapter<>(
                this, android.R.layout.simple_spinner_item, credentialTypes);
        credentialAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        credentialTypeSpinner.setAdapter(credentialAdapter);

        // قائمة البروفايلات (ستُحمل من قاعدة البيانات)
        String[] profiles = {"default", "1day", "1week", "1month"};
        ArrayAdapter<String> profileAdapter = new ArrayAdapter<>(
                this, android.R.layout.simple_spinner_item, profiles);
        profileAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        profileSpinner.setAdapter(profileAdapter);

        // قائمة وحدات البيانات (للـ Hotspot)
        if (systemType.equals("hotspot")) {
            String[] limitUnits = {"MB", "GB", "TB"};
            ArrayAdapter<String> unitAdapter = new ArrayAdapter<>(
                    this, android.R.layout.simple_spinner_item, limitUnits);
            unitAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
            limitUnitSpinner.setAdapter(unitAdapter);
        }
    }

    /**
     * إعداد ظهور الحقول حسب نوع النظام
     */
    private void setupFieldsVisibility() {
        if (systemType.equals("hotspot")) {
            // إظهار حقول Hotspot
            findViewById(R.id.layout_hotspot_fields).setVisibility(View.VISIBLE);
        } else {
            // إخفاء حقول Hotspot
            findViewById(R.id.layout_hotspot_fields).setVisibility(View.GONE);
        }
    }

    /**
     * إعداد القيم الافتراضية
     */
    private void setupDefaultValues() {
        portEdit.setText("8728"); // المنفذ الافتراضي لـ MikroTik
        countEdit.setText("10");
        lengthEdit.setText("6");

        if (systemType.equals("hotspot")) {
            limitBytesEdit.setText("1");
            limitUnitSpinner.setSelection(1); // GB
            daysEdit.setText("30");
        }
    }

    /**
     * إعداد مستمعي النقر
     */
    private void setupClickListeners() {
        // تبديل التبويبات
        tabLayout.addOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {
            @Override
            public void onTabSelected(TabLayout.Tab tab) {
                switchTab(tab.getPosition());
            }

            @Override
            public void onTabUnselected(TabLayout.Tab tab) {}

            @Override
            public void onTabReselected(TabLayout.Tab tab) {}
        });

        // زر الاتصال
        connectButton.setOnClickListener(v -> testConnection());

        // زر التوليد
        generateButton.setOnClickListener(v -> generateCredentials());

        // زر الحفظ
        saveButton.setOnClickListener(v -> saveCredentials());

        // زر تصدير PDF
        exportPdfButton.setOnClickListener(v -> exportToPdf());

        // زر الإرسال لـ MikroTik
        sendToMikrotikButton.setOnClickListener(v -> sendToMikroTik());
    }

    /**
     * تبديل التبويبات
     */
    private void switchTab(int position) {
        // إخفاء جميع الكروت
        connectionCard.setVisibility(View.GONE);
        generationCard.setVisibility(View.GONE);
        previewCard.setVisibility(View.GONE);

        // إظهار الكارت المطلوب
        switch (position) {
            case 0: // الاتصال
                connectionCard.setVisibility(View.VISIBLE);
                break;
            case 1: // التوليد
                generationCard.setVisibility(View.VISIBLE);
                break;
            case 2: // المعاينة
                previewCard.setVisibility(View.VISIBLE);
                updatePreview();
                break;
        }
    }

    /**
     * اختبار الاتصال
     */
    private void testConnection() {
        String ip = ipAddressEdit.getText().toString().trim();
        String port = portEdit.getText().toString().trim();
        String username = usernameEdit.getText().toString().trim();
        String password = passwordEdit.getText().toString().trim();

        // التحقق من صحة البيانات
        if (!ValidationUtils.isValidIpAddress(ip)) {
            showError("عنوان IP غير صحيح");
            return;
        }

        if (!ValidationUtils.isValidPort(port)) {
            showError("رقم المنفذ غير صحيح");
            return;
        }

        if (!ValidationUtils.isNotEmpty(username)) {
            showError("اسم المستخدم مطلوب");
            return;
        }

        // تنفيذ الاتصال في خيط منفصل
        progressBar.setVisibility(View.VISIBLE);
        connectionStatusText.setText("جاري الاتصال...");
        connectButton.setEnabled(false);

        // محاكاة الاتصال (يجب استبدالها بالاتصال الفعلي)
        new Thread(() -> {
            try {
                Thread.sleep(2000); // محاكاة وقت الاتصال

                runOnUiThread(() -> {
                    progressBar.setVisibility(View.GONE);
                    connectionStatusText.setText("✅ متصل");
                    connectionStatusText.setTextColor(getColor(R.color.success_color));
                    connectButton.setEnabled(true);
                    connectButton.setText("❌ قطع الاتصال");

                    // حفظ إعدادات الاتصال
                    saveConnectionSettings();

                    showSuccess("تم الاتصال بنجاح");
                });

            } catch (InterruptedException e) {
                runOnUiThread(() -> {
                    progressBar.setVisibility(View.GONE);
                    connectionStatusText.setText("❌ فشل الاتصال");
                    connectionStatusText.setTextColor(getColor(R.color.error_color));
                    connectButton.setEnabled(true);
                    showError("فشل في الاتصال");
                });
            }
        }).start();
    }

    /**
     * توليد بيانات الاعتماد
     */
    private void generateCredentials() {
        try {
            // الحصول على المعاملات
            String prefix = prefixEdit.getText().toString().trim();
            String suffix = suffixEdit.getText().toString().trim();
            int count = Integer.parseInt(countEdit.getText().toString().trim());
            int length = Integer.parseInt(lengthEdit.getText().toString().trim());
            String credentialType = credentialTypeSpinner.getSelectedItem().toString();
            String profile = profileSpinner.getSelectedItem().toString();
            String comment = commentEdit.getText().toString().trim();
            String location = locationEdit.getText().toString().trim();
            String price = priceEdit.getText().toString().trim();

            // التحقق من صحة البيانات
            if (count <= 0 || count > 10000) {
                showError("العدد يجب أن يكون بين 1 و 10000");
                return;
            }

            if (length <= 0 || length > 50) {
                showError("الطول يجب أن يكون بين 1 و 50");
                return;
            }

            progressBar.setVisibility(View.VISIBLE);
            generateButton.setEnabled(false);

            // تنفيذ التوليد في خيط منفصل
            new Thread(() -> {
                try {
                    if (systemType.equals("user_manager")) {
                        generateUserManagerCredentials(prefix, suffix, count, length,
                                credentialType, profile, comment, location, price);
                    } else {
                        generateHotspotCredentials(prefix, suffix, count, length,
                                credentialType, profile, comment, location, price);
                    }

                    runOnUiThread(() -> {
                        progressBar.setVisibility(View.GONE);
                        generateButton.setEnabled(true);
                        updateGeneratedCount();
                        showSuccess("تم التوليد بنجاح");

                        // الانتقال لتبويب المعاينة
                        tabLayout.getTabAt(2).select();
                    });

                } catch (Exception e) {
                    runOnUiThread(() -> {
                        progressBar.setVisibility(View.GONE);
                        generateButton.setEnabled(true);
                        showError("خطأ في التوليد: " + e.getMessage());
                    });
                }
            }).start();

        } catch (NumberFormatException e) {
            showError("يرجى إدخال أرقام صحيحة");
        }
    }

    /**
     * توليد بيانات User Manager
     */
    private void generateUserManagerCredentials(String prefix, String suffix, int count,
                                              int length, String credentialType, String profile,
                                              String comment, String location, String price) {
        userManagerCredentials.clear();

        for (int i = 0; i < count; i++) {
            UserManagerCredential credential = new UserManagerCredential();

            // توليد اسم المستخدم وكلمة المرور
            String username = generateRandomString(prefix, suffix, length, credentialType);
            String password = generateRandomString("", "", length, credentialType);

            credential.setUsername(username);
            credential.setPassword(password);
            credential.setProfile(profile);
            credential.setComment(comment);
            credential.setLocation(location);
            credential.setPrice(price);
            credential.setSerialNumber(i + 1);

            userManagerCredentials.add(credential);
        }
    }

    /**
     * توليد بيانات Hotspot
     */
    private void generateHotspotCredentials(String prefix, String suffix, int count,
                                          int length, String credentialType, String profile,
                                          String comment, String location, String price) {
        hotspotCredentials.clear();

        String limitBytes = limitBytesEdit.getText().toString().trim();
        String limitUnit = limitUnitSpinner.getSelectedItem().toString();
        String days = daysEdit.getText().toString().trim();

        for (int i = 0; i < count; i++) {
            HotspotCredential credential = new HotspotCredential();

            // توليد اسم المستخدم وكلمة المرور
            String username = generateRandomString(prefix, suffix, length, credentialType);
            String password = generateRandomString("", "", length, credentialType);

            credential.setUsername(username);
            credential.setPassword(password);
            credential.setProfile(profile);
            credential.setComment(comment);
            credential.setLocation(location);
            credential.setPrice(price);
            credential.setLimitBytes(limitBytes);
            credential.setLimitUnit(limitUnit);
            credential.setDays(days);
            credential.setSerialNumber(i + 1);

            hotspotCredentials.add(credential);
        }
    }

    /**
     * توليد نص عشوائي
     */
    private String generateRandomString(String prefix, String suffix, int length, String type) {
        String chars;
        switch (type) {
            case "أرقام":
                chars = "0123456789";
                break;
            case "حروف":
                chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
                break;
            default: // مختلط
                chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
                break;
        }

        Random random = new Random();
        StringBuilder result = new StringBuilder();

        if (prefix != null && !prefix.isEmpty()) {
            result.append(prefix);
        }

        for (int i = 0; i < length; i++) {
            result.append(chars.charAt(random.nextInt(chars.length())));
        }

        if (suffix != null && !suffix.isEmpty()) {
            result.append(suffix);
        }

        return result.toString();
    }

    /**
     * حفظ بيانات الاعتماد
     */
    private void saveCredentials() {
        // TODO: تنفيذ حفظ البيانات في قاعدة البيانات
        showSuccess("تم الحفظ بنجاح");
    }

    /**
     * تصدير إلى PDF
     */
    private void exportToPdf() {
        // TODO: تنفيذ تصدير PDF
        showSuccess("تم التصدير بنجاح");
    }

    /**
     * إرسال إلى MikroTik
     */
    private void sendToMikroTik() {
        // TODO: تنفيذ الإرسال لـ MikroTik
        showSuccess("تم الإرسال بنجاح");
    }

    /**
     * تحديث معاينة النتائج
     */
    private void updatePreview() {
        // TODO: تحديث RecyclerView بالبيانات المولدة
    }

    /**
     * تحديث عداد البيانات المولدة
     */
    private void updateGeneratedCount() {
        int count = systemType.equals("user_manager") ?
                userManagerCredentials.size() : hotspotCredentials.size();
        generatedCountText.setText("تم توليد " + count + " حساب");
    }

    /**
     * حفظ إعدادات الاتصال
     */
    private void saveConnectionSettings() {
        String ip = ipAddressEdit.getText().toString().trim();
        String port = portEdit.getText().toString().trim();
        String username = usernameEdit.getText().toString().trim();
        String password = passwordEdit.getText().toString().trim();

        app.saveSetting("connection_ip", ip);
        app.saveSetting("connection_port", port);
        app.saveSetting("connection_username", username);

        // تشفير وحفظ كلمة المرور
        String encryptedPassword = encryptionUtils.encryptPassword(password);
        app.saveSetting("connection_password", encryptedPassword);
    }

    /**
     * تحميل الإعدادات المحفوظة
     */
    private void loadSettings() {
        String ip = app.getSetting("connection_ip", "");
        String port = app.getSetting("connection_port", "8728");
        String username = app.getSetting("connection_username", "");
        String encryptedPassword = app.getSetting("connection_password", "");

        ipAddressEdit.setText(ip);
        portEdit.setText(port);
        usernameEdit.setText(username);

        if (!encryptedPassword.isEmpty()) {
            String password = encryptionUtils.decryptPassword(encryptedPassword);
            passwordEdit.setText(password);
        }
    }

    /**
     * عرض رسالة خطأ
     */
    private void showError(String message) {
        Toast.makeText(this, message, Toast.LENGTH_LONG).show();
    }

    /**
     * عرض رسالة نجاح
     */
    private void showSuccess(String message) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.card_generator_menu, menu);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int id = item.getItemId();
        if (id == android.R.id.home) {
            finish();
            return true;
        } else if (id == R.id.action_settings) {
            // فتح الإعدادات
            return true;
        } else if (id == R.id.action_help) {
            // عرض المساعدة
            return true;
        } else {
            return super.onOptionsItemSelected(item);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        Timber.i("تم إغلاق شاشة توليد الكروت");
    }
}
