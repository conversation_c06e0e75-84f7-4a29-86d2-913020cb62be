package com.mikrotik.cardgenerator.data.database;

import androidx.lifecycle.LiveData;
import androidx.room.*;
import com.mikrotik.cardgenerator.data.models.UserManagerCredential;
import java.util.List;

/**
 * DAO لحسابات User Manager
 */
@Dao
public interface UserManagerDao {
    
    @Query("SELECT * FROM user_manager_credentials ORDER BY created_date DESC")
    LiveData<List<UserManagerCredential>> getAllCredentials();
    
    @Query("SELECT * FROM user_manager_credentials WHERE is_active = 1 ORDER BY created_date DESC")
    LiveData<List<UserManagerCredential>> getActiveCredentials();
    
    @Query("SELECT * FROM user_manager_credentials WHERE id = :id")
    LiveData<UserManagerCredential> getCredentialById(int id);
    
    @Query("SELECT * FROM user_manager_credentials WHERE username = :username LIMIT 1")
    UserManagerCredential getCredentialByUsername(String username);
    
    @Query("SELECT * FROM user_manager_credentials WHERE profile = :profile ORDER BY created_date DESC")
    LiveData<List<UserManagerCredential>> getCredentialsByProfile(String profile);
    
    @Query("SELECT DISTINCT profile FROM user_manager_credentials WHERE profile IS NOT NULL AND profile != ''")
    LiveData<List<String>> getAllProfiles();
    
    @Query("SELECT COUNT(*) FROM user_manager_credentials")
    LiveData<Integer> getCredentialsCount();
    
    @Query("SELECT COUNT(*) FROM user_manager_credentials WHERE is_active = 1")
    LiveData<Integer> getActiveCredentialsCount();
    
    @Query("SELECT MAX(serial_number) FROM user_manager_credentials")
    int getLastSerialNumber();
    
    @Query("SELECT * FROM user_manager_credentials WHERE created_date BETWEEN :startDate AND :endDate ORDER BY created_date DESC")
    LiveData<List<UserManagerCredential>> getCredentialsByDateRange(long startDate, long endDate);
    
    @Query("SELECT * FROM user_manager_credentials WHERE username LIKE :searchQuery OR comment LIKE :searchQuery OR location LIKE :searchQuery ORDER BY created_date DESC")
    LiveData<List<UserManagerCredential>> searchCredentials(String searchQuery);
    
    @Insert
    long insertCredential(UserManagerCredential credential);
    
    @Insert
    List<Long> insertCredentials(List<UserManagerCredential> credentials);
    
    @Update
    void updateCredential(UserManagerCredential credential);
    
    @Delete
    void deleteCredential(UserManagerCredential credential);
    
    @Query("DELETE FROM user_manager_credentials WHERE id = :id")
    void deleteCredentialById(int id);
    
    @Query("DELETE FROM user_manager_credentials")
    void deleteAllCredentials();
    
    @Query("DELETE FROM user_manager_credentials WHERE is_active = 0")
    void deleteInactiveCredentials();
    
    @Query("UPDATE user_manager_credentials SET is_active = 0 WHERE id = :id")
    void deactivateCredential(int id);
    
    @Query("UPDATE user_manager_credentials SET is_active = 1 WHERE id = :id")
    void activateCredential(int id);
    
    @Query("UPDATE user_manager_credentials SET updated_date = :updateDate WHERE id = :id")
    void updateCredentialDate(int id, long updateDate);
}
