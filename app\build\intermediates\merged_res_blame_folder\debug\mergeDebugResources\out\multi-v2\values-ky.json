{"logs": [{"outputFile": "com.mikrotik.cardgenerator.app-mergeDebugResources-57:/values-ky/values-ky.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\69e8a0e0d964fb1e9e45715fb440dd3e\\transformed\\material-1.12.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,348,433,518,633,743,844,985,1069,1129,1193,1287,1357,1418,1505,1568,1632,1691,1765,1827,1881,1998,2056,2117,2171,2245,2367,2451,2530,2630,2716,2812,2944,3022,3100,3229,3318,3398,3459,3514,3580,3649,3726,3797,3878,3952,4028,4118,4191,4293,4378,4457,4547,4639,4713,4798,4888,4940,5024,5089,5174,5259,5321,5385,5448,5517,5634,5742,5842,5946,6011,6070,6152,6238,6314", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,82,84,84,114,109,100,140,83,59,63,93,69,60,86,62,63,58,73,61,53,116,57,60,53,73,121,83,78,99,85,95,131,77,77,128,88,79,60,54,65,68,76,70,80,73,75,89,72,101,84,78,89,91,73,84,89,51,83,64,84,84,61,63,62,68,116,107,99,103,64,58,81,85,75,82", "endOffsets": "260,343,428,513,628,738,839,980,1064,1124,1188,1282,1352,1413,1500,1563,1627,1686,1760,1822,1876,1993,2051,2112,2166,2240,2362,2446,2525,2625,2711,2807,2939,3017,3095,3224,3313,3393,3454,3509,3575,3644,3721,3792,3873,3947,4023,4113,4186,4288,4373,4452,4542,4634,4708,4793,4883,4935,5019,5084,5169,5254,5316,5380,5443,5512,5629,5737,5837,5941,6006,6065,6147,6233,6309,6392"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,113,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3027,3110,3195,3280,3395,4236,4337,4478,4562,4622,4686,4780,4850,4911,4998,5061,5125,5184,5258,5320,5374,5491,5549,5610,5664,5738,5860,5944,6023,6123,6209,6305,6437,6515,6593,6722,6811,6891,6952,7007,7073,7142,7219,7290,7371,7445,7521,7611,7684,7786,7871,7950,8040,8132,8206,8291,8381,8433,8517,8582,8667,8752,8814,8878,8941,9010,9127,9235,9335,9439,9504,9791,9955,10041,10117", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,113,115,116,117", "endColumns": "12,82,84,84,114,109,100,140,83,59,63,93,69,60,86,62,63,58,73,61,53,116,57,60,53,73,121,83,78,99,85,95,131,77,77,128,88,79,60,54,65,68,76,70,80,73,75,89,72,101,84,78,89,91,73,84,89,51,83,64,84,84,61,63,62,68,116,107,99,103,64,58,81,85,75,82", "endOffsets": "310,3105,3190,3275,3390,3500,4332,4473,4557,4617,4681,4775,4845,4906,4993,5056,5120,5179,5253,5315,5369,5486,5544,5605,5659,5733,5855,5939,6018,6118,6204,6300,6432,6510,6588,6717,6806,6886,6947,7002,7068,7137,7214,7285,7366,7440,7516,7606,7679,7781,7866,7945,8035,8127,8201,8286,8376,8428,8512,8577,8662,8747,8809,8873,8936,9005,9122,9230,9330,9434,9499,9558,9868,10036,10112,10195"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fb7148cc8318b09aaa0d36d8e3e2b12c\\transformed\\navigation-ui-2.7.6\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,165", "endColumns": "109,117", "endOffsets": "160,278"}, "to": {"startLines": "111,112", "startColumns": "4,4", "startOffsets": "9563,9673", "endColumns": "109,117", "endOffsets": "9668,9786"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\476525ada1d35df1ee329f5bd94fbe69\\transformed\\core-1.13.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,360,467,571,675,786", "endColumns": "99,101,102,106,103,103,110,100", "endOffsets": "150,252,355,462,566,670,781,882"}, "to": {"startLines": "38,39,40,41,42,43,44,118", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3505,3605,3707,3810,3917,4021,4125,10200", "endColumns": "99,101,102,106,103,103,110,100", "endOffsets": "3600,3702,3805,3912,4016,4120,4231,10296"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\412d4fb45e796eeac261181f9a418e93\\transformed\\appcompat-1.7.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,325,437,522,627,744,823,901,992,1085,1180,1274,1374,1467,1562,1657,1748,1839,1920,2026,2131,2229,2336,2439,2554,2715,2817", "endColumns": "110,108,111,84,104,116,78,77,90,92,94,93,99,92,94,94,90,90,80,105,104,97,106,102,114,160,101,81", "endOffsets": "211,320,432,517,622,739,818,896,987,1080,1175,1269,1369,1462,1557,1652,1743,1834,1915,2021,2126,2224,2331,2434,2549,2710,2812,2894"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,426,535,647,732,837,954,1033,1111,1202,1295,1390,1484,1584,1677,1772,1867,1958,2049,2130,2236,2341,2439,2546,2649,2764,2925,9873", "endColumns": "110,108,111,84,104,116,78,77,90,92,94,93,99,92,94,94,90,90,80,105,104,97,106,102,114,160,101,81", "endOffsets": "421,530,642,727,832,949,1028,1106,1197,1290,1385,1479,1579,1672,1767,1862,1953,2044,2125,2231,2336,2434,2541,2644,2759,2920,3022,9950"}}]}]}