<dependencies>
  <compile
      roots="androidx.databinding:databinding-runtime:8.10.1@aar,androidx.databinding:viewbinding:8.10.1@aar,androidx.databinding:databinding-adapters:8.10.1@aar,androidx.databinding:databinding-common:8.10.1@jar,com.karumi:dexter:6.2.3@aar,androidx.navigation:navigation-common:2.7.6@aar,androidx.navigation:navigation-common-ktx:2.7.6@aar,androidx.navigation:navigation-runtime:2.7.6@aar,androidx.navigation:navigation-runtime-ktx:2.7.6@aar,androidx.navigation:navigation-fragment:2.7.6@aar,androidx.navigation:navigation-fragment-ktx:2.7.6@aar,androidx.navigation:navigation-ui-ktx:2.7.6@aar,androidx.navigation:navigation-ui:2.7.6@aar,com.google.android.material:material:1.12.0@aar,androidx.appcompat:appcompat-resources:1.7.1@aar,androidx.appcompat:appcompat:1.7.1@aar,com.github.bumptech.glide:glide:4.16.0@aar,androidx.viewpager2:viewpager2:1.1.0-beta02@aar,androidx.fragment:fragment:1.6.2@aar,androidx.fragment:fragment-ktx:1.6.2@aar,androidx.activity:activity:1.8.2@aar,androidx.activity:activity-ktx:1.8.2@aar,androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar,androidx.lifecycle:lifecycle-common:2.7.0@jar,androidx.lifecycle:lifecycle-runtime-ktx:2.7.0@aar,androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0@aar,androidx.dynamicanimation:dynamicanimation:1.0.0@aar,androidx.legacy:legacy-support-core-utils:1.0.0@aar,androidx.loader:loader:1.0.0@aar,androidx.lifecycle:lifecycle-livedata:2.7.0@aar,androidx.lifecycle:lifecycle-livedata:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar,androidx.lifecycle:lifecycle-livedata-ktx:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0@aar,androidx.recyclerview:recyclerview:1.3.2@aar,androidx.drawerlayout:drawerlayout:1.1.1@aar,androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar,androidx.transition:transition:1.5.0@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar,androidx.customview:customview:1.1.0@aar,androidx.core:core:1.13.0@aar,androidx.core:core:1.13.0@aar,androidx.lifecycle:lifecycle-runtime:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0@aar,androidx.core:core-ktx:1.13.0@aar,androidx.constraintlayout:constraintlayout:2.1.4@aar,androidx.cardview:cardview:1.0.0@aar,androidx.room:room-common:2.6.1@jar,androidx.room:room-ktx:2.6.1@aar,androidx.room:room-runtime:2.6.1@aar,com.squareup.retrofit2:converter-gson:2.9.0@jar,com.squareup.retrofit2:retrofit:2.9.0@jar,com.squareup.okhttp3:logging-interceptor:4.12.0@jar,com.squareup.okhttp3:okhttp:4.12.0@jar,com.google.code.gson:gson:2.10.1@jar,com.itextpdf:sign:8.0.2@jar,com.itextpdf:pdfa:8.0.2@jar,com.itextpdf:forms:8.0.2@jar,com.itextpdf:svg:8.0.2@jar,com.itextpdf:styled-xml-parser:8.0.2@jar,com.itextpdf:layout:8.0.2@jar,com.itextpdf:barcodes:8.0.2@jar,com.itextpdf:kernel:8.0.2@jar,com.itextpdf:io:8.0.2@jar,com.journeyapps:zxing-android-embedded:4.3.0@aar,com.google.zxing:core:3.5.2@jar,androidx.security:security-crypto:1.1.0-alpha06@aar,com.opencsv:opencsv:5.8@jar,org.apache.poi:poi-ooxml:5.2.4@jar,org.apache.poi:poi:5.2.4@jar,com.jakewharton.timber:timber:5.0.1@aar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.savedstate:savedstate-ktx:1.2.1@aar,androidx.savedstate:savedstate:1.2.1@aar,androidx.sqlite:sqlite-framework:2.4.0@aar,androidx.sqlite:sqlite:2.4.0@aar,com.github.bumptech.glide:gifdecoder:4.16.0@aar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.collection:collection-ktx:1.1.0@jar,androidx.collection:collection:1.1.0@jar,androidx.interpolator:interpolator:1.0.0@aar,androidx.documentfile:documentfile:1.0.0@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar,androidx.print:print:1.0.0@aar,androidx.annotation:annotation-jvm:1.6.0@jar,androidx.annotation:annotation-experimental:1.4.0@aar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.1@jar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.1@jar,com.squareup.okio:okio-jvm:3.6.0@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar,org.jetbrains.kotlin:kotlin-stdlib:1.9.10@jar,org.jetbrains.kotlin:kotlin-stdlib-common:1.9.10@jar,org.jetbrains:annotations:23.0.0@jar,androidx.tracing:tracing:1.0.0@aar,androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar,com.itextpdf:bouncy-castle-connector:8.0.2@jar,com.itextpdf:commons:8.0.2@jar,com.itextpdf:font-asian:8.0.2@jar,com.itextpdf:hyph:8.0.2@jar,org.slf4j:slf4j-api:1.7.36@jar,com.github.bumptech.glide:disklrucache:4.16.0@jar,com.github.bumptech.glide:annotations:4.16.0@jar,androidx.exifinterface:exifinterface:1.3.6@aar,org.apache.commons:commons-text:1.10.0@jar,org.apache.commons:commons-lang3:3.12.0@jar,commons-beanutils:commons-beanutils:1.9.4@jar,commons-logging:commons-logging:1.2@jar,commons-collections:commons-collections:3.2.2@jar,org.apache.commons:commons-collections4:4.4@jar,commons-codec:commons-codec:1.16.0@jar,org.apache.commons:commons-math3:3.6.1@jar,commons-io:commons-io:2.13.0@jar,com.zaxxer:SparseBitSet:1.3@jar,org.apache.poi:poi-ooxml-lite:5.2.4@jar,org.apache.xmlbeans:xmlbeans:5.1.1@jar,org.apache.logging.log4j:log4j-api:2.20.0@jar,org.apache.commons:commons-compress:1.24.0@jar,com.github.virtuald:curvesapi:1.08@jar">
    <dependency
        name="androidx.databinding:databinding-runtime:8.10.1@aar"
        simpleName="androidx.databinding:databinding-runtime"/>
    <dependency
        name="androidx.databinding:viewbinding:8.10.1@aar"
        simpleName="androidx.databinding:viewbinding"/>
    <dependency
        name="androidx.databinding:databinding-adapters:8.10.1@aar"
        simpleName="androidx.databinding:databinding-adapters"/>
    <dependency
        name="androidx.databinding:databinding-common:8.10.1@jar"
        simpleName="androidx.databinding:databinding-common"/>
    <dependency
        name="com.karumi:dexter:6.2.3@aar"
        simpleName="com.karumi:dexter"/>
    <dependency
        name="androidx.navigation:navigation-common:2.7.6@aar"
        simpleName="androidx.navigation:navigation-common"/>
    <dependency
        name="androidx.navigation:navigation-common-ktx:2.7.6@aar"
        simpleName="androidx.navigation:navigation-common-ktx"/>
    <dependency
        name="androidx.navigation:navigation-runtime:2.7.6@aar"
        simpleName="androidx.navigation:navigation-runtime"/>
    <dependency
        name="androidx.navigation:navigation-runtime-ktx:2.7.6@aar"
        simpleName="androidx.navigation:navigation-runtime-ktx"/>
    <dependency
        name="androidx.navigation:navigation-fragment:2.7.6@aar"
        simpleName="androidx.navigation:navigation-fragment"/>
    <dependency
        name="androidx.navigation:navigation-fragment-ktx:2.7.6@aar"
        simpleName="androidx.navigation:navigation-fragment-ktx"/>
    <dependency
        name="androidx.navigation:navigation-ui-ktx:2.7.6@aar"
        simpleName="androidx.navigation:navigation-ui-ktx"/>
    <dependency
        name="androidx.navigation:navigation-ui:2.7.6@aar"
        simpleName="androidx.navigation:navigation-ui"/>
    <dependency
        name="com.google.android.material:material:1.12.0@aar"
        simpleName="com.google.android.material:material"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.7.1@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="androidx.appcompat:appcompat:1.7.1@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="com.github.bumptech.glide:glide:4.16.0@aar"
        simpleName="com.github.bumptech.glide:glide"/>
    <dependency
        name="androidx.viewpager2:viewpager2:1.1.0-beta02@aar"
        simpleName="androidx.viewpager2:viewpager2"/>
    <dependency
        name="androidx.fragment:fragment:1.6.2@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.fragment:fragment-ktx:1.6.2@aar"
        simpleName="androidx.fragment:fragment-ktx"/>
    <dependency
        name="androidx.activity:activity:1.8.2@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.activity:activity-ktx:1.8.2@aar"
        simpleName="androidx.activity:activity-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common:2.7.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-ktx:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core-ktx"/>
    <dependency
        name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
        simpleName="androidx.dynamicanimation:dynamicanimation"/>
    <dependency
        name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-utils"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-ktx:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-ktx"/>
    <dependency
        name="androidx.recyclerview:recyclerview:1.3.2@aar"
        simpleName="androidx.recyclerview:recyclerview"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar"
        simpleName="androidx.coordinatorlayout:coordinatorlayout"/>
    <dependency
        name="androidx.transition:transition:1.5.0@aar"
        simpleName="androidx.transition:transition"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar"
        simpleName="androidx.slidingpanelayout:slidingpanelayout"/>
    <dependency
        name="androidx.customview:customview:1.1.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.core:core:1.13.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.core:core-ktx:1.13.0@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout:2.1.4@aar"
        simpleName="androidx.constraintlayout:constraintlayout"/>
    <dependency
        name="androidx.cardview:cardview:1.0.0@aar"
        simpleName="androidx.cardview:cardview"/>
    <dependency
        name="androidx.room:room-common:2.6.1@jar"
        simpleName="androidx.room:room-common"/>
    <dependency
        name="androidx.room:room-ktx:2.6.1@aar"
        simpleName="androidx.room:room-ktx"/>
    <dependency
        name="androidx.room:room-runtime:2.6.1@aar"
        simpleName="androidx.room:room-runtime"/>
    <dependency
        name="com.squareup.retrofit2:converter-gson:2.9.0@jar"
        simpleName="com.squareup.retrofit2:converter-gson"/>
    <dependency
        name="com.squareup.retrofit2:retrofit:2.9.0@jar"
        simpleName="com.squareup.retrofit2:retrofit"/>
    <dependency
        name="com.squareup.okhttp3:logging-interceptor:4.12.0@jar"
        simpleName="com.squareup.okhttp3:logging-interceptor"/>
    <dependency
        name="com.squareup.okhttp3:okhttp:4.12.0@jar"
        simpleName="com.squareup.okhttp3:okhttp"/>
    <dependency
        name="com.google.code.gson:gson:2.10.1@jar"
        simpleName="com.google.code.gson:gson"/>
    <dependency
        name="com.itextpdf:sign:8.0.2@jar"
        simpleName="com.itextpdf:sign"/>
    <dependency
        name="com.itextpdf:pdfa:8.0.2@jar"
        simpleName="com.itextpdf:pdfa"/>
    <dependency
        name="com.itextpdf:forms:8.0.2@jar"
        simpleName="com.itextpdf:forms"/>
    <dependency
        name="com.itextpdf:svg:8.0.2@jar"
        simpleName="com.itextpdf:svg"/>
    <dependency
        name="com.itextpdf:styled-xml-parser:8.0.2@jar"
        simpleName="com.itextpdf:styled-xml-parser"/>
    <dependency
        name="com.itextpdf:layout:8.0.2@jar"
        simpleName="com.itextpdf:layout"/>
    <dependency
        name="com.itextpdf:barcodes:8.0.2@jar"
        simpleName="com.itextpdf:barcodes"/>
    <dependency
        name="com.itextpdf:kernel:8.0.2@jar"
        simpleName="com.itextpdf:kernel"/>
    <dependency
        name="com.itextpdf:io:8.0.2@jar"
        simpleName="com.itextpdf:io"/>
    <dependency
        name="com.journeyapps:zxing-android-embedded:4.3.0@aar"
        simpleName="com.journeyapps:zxing-android-embedded"/>
    <dependency
        name="com.google.zxing:core:3.5.2@jar"
        simpleName="com.google.zxing:core"/>
    <dependency
        name="androidx.security:security-crypto:1.1.0-alpha06@aar"
        simpleName="androidx.security:security-crypto"/>
    <dependency
        name="com.opencsv:opencsv:5.8@jar"
        simpleName="com.opencsv:opencsv"/>
    <dependency
        name="org.apache.poi:poi-ooxml:5.2.4@jar"
        simpleName="org.apache.poi:poi-ooxml"/>
    <dependency
        name="org.apache.poi:poi:5.2.4@jar"
        simpleName="org.apache.poi:poi"/>
    <dependency
        name="com.jakewharton.timber:timber:5.0.1@aar"
        simpleName="com.jakewharton.timber:timber"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate-ktx"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.sqlite:sqlite-framework:2.4.0@aar"
        simpleName="androidx.sqlite:sqlite-framework"/>
    <dependency
        name="androidx.sqlite:sqlite:2.4.0@aar"
        simpleName="androidx.sqlite:sqlite"/>
    <dependency
        name="com.github.bumptech.glide:gifdecoder:4.16.0@aar"
        simpleName="com.github.bumptech.glide:gifdecoder"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection-ktx:1.1.0@jar"
        simpleName="androidx.collection:collection-ktx"/>
    <dependency
        name="androidx.collection:collection:1.1.0@jar"
        simpleName="androidx.collection:collection"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.documentfile:documentfile:1.0.0@aar"
        simpleName="androidx.documentfile:documentfile"/>
    <dependency
        name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
        simpleName="androidx.localbroadcastmanager:localbroadcastmanager"/>
    <dependency
        name="androidx.print:print:1.0.0@aar"
        simpleName="androidx.print:print"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.6.0@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.0@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="com.squareup.okio:okio-jvm:3.6.0@jar"
        simpleName="com.squareup.okio:okio-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-common:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-common"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="androidx.tracing:tracing:1.0.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
        simpleName="androidx.resourceinspection:resourceinspection-annotation"/>
    <dependency
        name="com.itextpdf:bouncy-castle-connector:8.0.2@jar"
        simpleName="com.itextpdf:bouncy-castle-connector"/>
    <dependency
        name="com.itextpdf:commons:8.0.2@jar"
        simpleName="com.itextpdf:commons"/>
    <dependency
        name="com.itextpdf:font-asian:8.0.2@jar"
        simpleName="com.itextpdf:font-asian"/>
    <dependency
        name="com.itextpdf:hyph:8.0.2@jar"
        simpleName="com.itextpdf:hyph"/>
    <dependency
        name="org.slf4j:slf4j-api:1.7.36@jar"
        simpleName="org.slf4j:slf4j-api"/>
    <dependency
        name="com.github.bumptech.glide:disklrucache:4.16.0@jar"
        simpleName="com.github.bumptech.glide:disklrucache"/>
    <dependency
        name="com.github.bumptech.glide:annotations:4.16.0@jar"
        simpleName="com.github.bumptech.glide:annotations"/>
    <dependency
        name="androidx.exifinterface:exifinterface:1.3.6@aar"
        simpleName="androidx.exifinterface:exifinterface"/>
    <dependency
        name="org.apache.commons:commons-text:1.10.0@jar"
        simpleName="org.apache.commons:commons-text"/>
    <dependency
        name="org.apache.commons:commons-lang3:3.12.0@jar"
        simpleName="org.apache.commons:commons-lang3"/>
    <dependency
        name="commons-beanutils:commons-beanutils:1.9.4@jar"
        simpleName="commons-beanutils:commons-beanutils"/>
    <dependency
        name="commons-logging:commons-logging:1.2@jar"
        simpleName="commons-logging:commons-logging"/>
    <dependency
        name="commons-collections:commons-collections:3.2.2@jar"
        simpleName="commons-collections:commons-collections"/>
    <dependency
        name="org.apache.commons:commons-collections4:4.4@jar"
        simpleName="org.apache.commons:commons-collections4"/>
    <dependency
        name="commons-codec:commons-codec:1.16.0@jar"
        simpleName="commons-codec:commons-codec"/>
    <dependency
        name="org.apache.commons:commons-math3:3.6.1@jar"
        simpleName="org.apache.commons:commons-math3"/>
    <dependency
        name="commons-io:commons-io:2.13.0@jar"
        simpleName="commons-io:commons-io"/>
    <dependency
        name="com.zaxxer:SparseBitSet:1.3@jar"
        simpleName="com.zaxxer:SparseBitSet"/>
    <dependency
        name="org.apache.poi:poi-ooxml-lite:5.2.4@jar"
        simpleName="org.apache.poi:poi-ooxml-lite"/>
    <dependency
        name="org.apache.xmlbeans:xmlbeans:5.1.1@jar"
        simpleName="org.apache.xmlbeans:xmlbeans"/>
    <dependency
        name="org.apache.logging.log4j:log4j-api:2.20.0@jar"
        simpleName="org.apache.logging.log4j:log4j-api"/>
    <dependency
        name="org.apache.commons:commons-compress:1.24.0@jar"
        simpleName="org.apache.commons:commons-compress"/>
    <dependency
        name="com.github.virtuald:curvesapi:1.08@jar"
        simpleName="com.github.virtuald:curvesapi"/>
  </compile>
  <package
      roots="androidx.databinding:databinding-adapters:8.10.1@aar,androidx.databinding:databinding-runtime:8.10.1@aar,androidx.databinding:viewbinding:8.10.1@aar,androidx.databinding:databinding-common:8.10.1@jar,com.karumi:dexter:6.2.3@aar,androidx.navigation:navigation-common:2.7.6@aar,androidx.navigation:navigation-common-ktx:2.7.6@aar,androidx.navigation:navigation-runtime:2.7.6@aar,androidx.navigation:navigation-runtime-ktx:2.7.6@aar,androidx.navigation:navigation-fragment:2.7.6@aar,androidx.navigation:navigation-fragment-ktx:2.7.6@aar,androidx.navigation:navigation-ui-ktx:2.7.6@aar,androidx.navigation:navigation-ui:2.7.6@aar,com.google.android.material:material:1.12.0@aar,androidx.constraintlayout:constraintlayout:2.1.4@aar,androidx.appcompat:appcompat-resources:1.7.1@aar,androidx.appcompat:appcompat:1.7.1@aar,com.github.bumptech.glide:glide:4.16.0@aar,androidx.recyclerview:recyclerview:1.3.2@aar,androidx.viewpager2:viewpager2:1.1.0-beta02@aar,androidx.fragment:fragment:1.6.2@aar,androidx.fragment:fragment-ktx:1.6.2@aar,androidx.activity:activity:1.8.2@aar,androidx.activity:activity-ktx:1.8.2@aar,androidx.emoji2:emoji2-views-helper:1.3.0@aar,androidx.emoji2:emoji2:1.3.0@aar,androidx.drawerlayout:drawerlayout:1.1.1@aar,androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar,androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar,androidx.transition:transition:1.5.0@aar,androidx.dynamicanimation:dynamicanimation:1.0.0@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.customview:customview:1.1.0@aar,androidx.legacy:legacy-support-core-utils:1.0.0@aar,androidx.loader:loader:1.0.0@aar,androidx.window:window:1.0.0@aar,androidx.core:core:1.13.0@aar,androidx.core:core:1.13.0@aar,androidx.customview:customview-poolingcontainer:1.0.0@aar,androidx.savedstate:savedstate-ktx:1.2.1@aar,androidx.savedstate:savedstate:1.2.1@aar,androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar,androidx.lifecycle:lifecycle-common:2.7.0@jar,androidx.lifecycle:lifecycle-process:2.7.0@aar,androidx.lifecycle:lifecycle-runtime-ktx:2.7.0@aar,androidx.lifecycle:lifecycle-livedata:2.7.0@aar,androidx.lifecycle:lifecycle-livedata:2.7.0@aar,androidx.lifecycle:lifecycle-runtime:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0@aar,androidx.lifecycle:lifecycle-livedata-ktx:2.7.0@aar,androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0@aar,androidx.core:core-ktx:1.13.0@aar,androidx.cardview:cardview:1.0.0@aar,androidx.room:room-common:2.6.1@jar,androidx.room:room-ktx:2.6.1@aar,androidx.room:room-runtime:2.6.1@aar,com.squareup.retrofit2:converter-gson:2.9.0@jar,com.squareup.retrofit2:retrofit:2.9.0@jar,com.squareup.okhttp3:logging-interceptor:4.12.0@jar,com.squareup.okhttp3:okhttp:4.12.0@jar,androidx.security:security-crypto:1.1.0-alpha06@aar,com.google.crypto.tink:tink-android:1.8.0@jar,com.google.code.gson:gson:2.10.1@jar,com.itextpdf:sign:8.0.2@jar,com.itextpdf:pdfa:8.0.2@jar,com.itextpdf:forms:8.0.2@jar,com.itextpdf:svg:8.0.2@jar,com.itextpdf:styled-xml-parser:8.0.2@jar,com.itextpdf:layout:8.0.2@jar,com.itextpdf:barcodes:8.0.2@jar,com.itextpdf:kernel:8.0.2@jar,com.itextpdf:io:8.0.2@jar,com.journeyapps:zxing-android-embedded:4.3.0@aar,com.google.zxing:core:3.5.2@jar,com.opencsv:opencsv:5.8@jar,org.apache.poi:poi-ooxml:5.2.4@jar,org.apache.poi:poi:5.2.4@jar,com.jakewharton.timber:timber:5.0.1@aar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.profileinstaller:profileinstaller:1.3.1@aar,androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.sqlite:sqlite-framework:2.4.0@aar,androidx.sqlite:sqlite:2.4.0@aar,com.github.bumptech.glide:gifdecoder:4.16.0@aar,androidx.exifinterface:exifinterface:1.3.6@aar,androidx.startup:startup-runtime:1.1.1@aar,androidx.tracing:tracing:1.0.0@aar,androidx.collection:collection-ktx:1.1.0@jar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.collection:collection:1.1.0@jar,androidx.interpolator:interpolator:1.0.0@aar,androidx.concurrent:concurrent-futures:1.1.0@jar,androidx.arch.core:core-common:2.2.0@jar,androidx.documentfile:documentfile:1.0.0@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar,androidx.print:print:1.0.0@aar,androidx.annotation:annotation-experimental:1.4.0@aar,androidx.annotation:annotation-jvm:1.6.0@jar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.1@jar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.1@jar,com.squareup.okio:okio-jvm:3.6.0@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar,org.jetbrains.kotlin:kotlin-stdlib:1.9.10@jar,com.google.errorprone:error_prone_annotations:2.15.0@jar,androidx.constraintlayout:constraintlayout-core:1.0.4@jar,com.itextpdf:bouncy-castle-connector:8.0.2@jar,com.itextpdf:commons:8.0.2@jar,com.itextpdf:font-asian:8.0.2@jar,com.itextpdf:hyph:8.0.2@jar,org.slf4j:slf4j-api:1.7.36@jar,com.github.bumptech.glide:disklrucache:4.16.0@jar,com.github.bumptech.glide:annotations:4.16.0@jar,org.apache.commons:commons-text:1.10.0@jar,org.apache.commons:commons-lang3:3.12.0@jar,commons-beanutils:commons-beanutils:1.9.4@jar,org.apache.commons:commons-collections4:4.4@jar,commons-codec:commons-codec:1.16.0@jar,org.apache.commons:commons-math3:3.6.1@jar,commons-io:commons-io:2.13.0@jar,com.zaxxer:SparseBitSet:1.3@jar,org.apache.poi:poi-ooxml-lite:5.2.4@jar,org.apache.xmlbeans:xmlbeans:5.1.1@jar,org.apache.logging.log4j:log4j-api:2.20.0@jar,org.apache.commons:commons-compress:1.24.0@jar,com.github.virtuald:curvesapi:1.08@jar,org.jetbrains:annotations:23.0.0@jar,com.google.guava:listenablefuture:1.0@jar,org.jetbrains.kotlin:kotlin-stdlib-common:1.9.10@jar,commons-logging:commons-logging:1.2@jar,commons-collections:commons-collections:3.2.2@jar">
    <dependency
        name="androidx.databinding:databinding-adapters:8.10.1@aar"
        simpleName="androidx.databinding:databinding-adapters"/>
    <dependency
        name="androidx.databinding:databinding-runtime:8.10.1@aar"
        simpleName="androidx.databinding:databinding-runtime"/>
    <dependency
        name="androidx.databinding:viewbinding:8.10.1@aar"
        simpleName="androidx.databinding:viewbinding"/>
    <dependency
        name="androidx.databinding:databinding-common:8.10.1@jar"
        simpleName="androidx.databinding:databinding-common"/>
    <dependency
        name="com.karumi:dexter:6.2.3@aar"
        simpleName="com.karumi:dexter"/>
    <dependency
        name="androidx.navigation:navigation-common:2.7.6@aar"
        simpleName="androidx.navigation:navigation-common"/>
    <dependency
        name="androidx.navigation:navigation-common-ktx:2.7.6@aar"
        simpleName="androidx.navigation:navigation-common-ktx"/>
    <dependency
        name="androidx.navigation:navigation-runtime:2.7.6@aar"
        simpleName="androidx.navigation:navigation-runtime"/>
    <dependency
        name="androidx.navigation:navigation-runtime-ktx:2.7.6@aar"
        simpleName="androidx.navigation:navigation-runtime-ktx"/>
    <dependency
        name="androidx.navigation:navigation-fragment:2.7.6@aar"
        simpleName="androidx.navigation:navigation-fragment"/>
    <dependency
        name="androidx.navigation:navigation-fragment-ktx:2.7.6@aar"
        simpleName="androidx.navigation:navigation-fragment-ktx"/>
    <dependency
        name="androidx.navigation:navigation-ui-ktx:2.7.6@aar"
        simpleName="androidx.navigation:navigation-ui-ktx"/>
    <dependency
        name="androidx.navigation:navigation-ui:2.7.6@aar"
        simpleName="androidx.navigation:navigation-ui"/>
    <dependency
        name="com.google.android.material:material:1.12.0@aar"
        simpleName="com.google.android.material:material"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout:2.1.4@aar"
        simpleName="androidx.constraintlayout:constraintlayout"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.7.1@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="androidx.appcompat:appcompat:1.7.1@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="com.github.bumptech.glide:glide:4.16.0@aar"
        simpleName="com.github.bumptech.glide:glide"/>
    <dependency
        name="androidx.recyclerview:recyclerview:1.3.2@aar"
        simpleName="androidx.recyclerview:recyclerview"/>
    <dependency
        name="androidx.viewpager2:viewpager2:1.1.0-beta02@aar"
        simpleName="androidx.viewpager2:viewpager2"/>
    <dependency
        name="androidx.fragment:fragment:1.6.2@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.fragment:fragment-ktx:1.6.2@aar"
        simpleName="androidx.fragment:fragment-ktx"/>
    <dependency
        name="androidx.activity:activity:1.8.2@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.activity:activity-ktx:1.8.2@aar"
        simpleName="androidx.activity:activity-ktx"/>
    <dependency
        name="androidx.emoji2:emoji2-views-helper:1.3.0@aar"
        simpleName="androidx.emoji2:emoji2-views-helper"/>
    <dependency
        name="androidx.emoji2:emoji2:1.3.0@aar"
        simpleName="androidx.emoji2:emoji2"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar"
        simpleName="androidx.coordinatorlayout:coordinatorlayout"/>
    <dependency
        name="androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar"
        simpleName="androidx.slidingpanelayout:slidingpanelayout"/>
    <dependency
        name="androidx.transition:transition:1.5.0@aar"
        simpleName="androidx.transition:transition"/>
    <dependency
        name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
        simpleName="androidx.dynamicanimation:dynamicanimation"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.customview:customview:1.1.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-utils"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.window:window:1.0.0@aar"
        simpleName="androidx.window:window"/>
    <dependency
        name="androidx.core:core:1.13.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.customview:customview-poolingcontainer:1.0.0@aar"
        simpleName="androidx.customview:customview-poolingcontainer"/>
    <dependency
        name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate-ktx"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common:2.7.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common"/>
    <dependency
        name="androidx.lifecycle:lifecycle-process:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-process"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-ktx:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-ktx:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.core:core-ktx:1.13.0@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.cardview:cardview:1.0.0@aar"
        simpleName="androidx.cardview:cardview"/>
    <dependency
        name="androidx.room:room-common:2.6.1@jar"
        simpleName="androidx.room:room-common"/>
    <dependency
        name="androidx.room:room-ktx:2.6.1@aar"
        simpleName="androidx.room:room-ktx"/>
    <dependency
        name="androidx.room:room-runtime:2.6.1@aar"
        simpleName="androidx.room:room-runtime"/>
    <dependency
        name="com.squareup.retrofit2:converter-gson:2.9.0@jar"
        simpleName="com.squareup.retrofit2:converter-gson"/>
    <dependency
        name="com.squareup.retrofit2:retrofit:2.9.0@jar"
        simpleName="com.squareup.retrofit2:retrofit"/>
    <dependency
        name="com.squareup.okhttp3:logging-interceptor:4.12.0@jar"
        simpleName="com.squareup.okhttp3:logging-interceptor"/>
    <dependency
        name="com.squareup.okhttp3:okhttp:4.12.0@jar"
        simpleName="com.squareup.okhttp3:okhttp"/>
    <dependency
        name="androidx.security:security-crypto:1.1.0-alpha06@aar"
        simpleName="androidx.security:security-crypto"/>
    <dependency
        name="com.google.crypto.tink:tink-android:1.8.0@jar"
        simpleName="com.google.crypto.tink:tink-android"/>
    <dependency
        name="com.google.code.gson:gson:2.10.1@jar"
        simpleName="com.google.code.gson:gson"/>
    <dependency
        name="com.itextpdf:sign:8.0.2@jar"
        simpleName="com.itextpdf:sign"/>
    <dependency
        name="com.itextpdf:pdfa:8.0.2@jar"
        simpleName="com.itextpdf:pdfa"/>
    <dependency
        name="com.itextpdf:forms:8.0.2@jar"
        simpleName="com.itextpdf:forms"/>
    <dependency
        name="com.itextpdf:svg:8.0.2@jar"
        simpleName="com.itextpdf:svg"/>
    <dependency
        name="com.itextpdf:styled-xml-parser:8.0.2@jar"
        simpleName="com.itextpdf:styled-xml-parser"/>
    <dependency
        name="com.itextpdf:layout:8.0.2@jar"
        simpleName="com.itextpdf:layout"/>
    <dependency
        name="com.itextpdf:barcodes:8.0.2@jar"
        simpleName="com.itextpdf:barcodes"/>
    <dependency
        name="com.itextpdf:kernel:8.0.2@jar"
        simpleName="com.itextpdf:kernel"/>
    <dependency
        name="com.itextpdf:io:8.0.2@jar"
        simpleName="com.itextpdf:io"/>
    <dependency
        name="com.journeyapps:zxing-android-embedded:4.3.0@aar"
        simpleName="com.journeyapps:zxing-android-embedded"/>
    <dependency
        name="com.google.zxing:core:3.5.2@jar"
        simpleName="com.google.zxing:core"/>
    <dependency
        name="com.opencsv:opencsv:5.8@jar"
        simpleName="com.opencsv:opencsv"/>
    <dependency
        name="org.apache.poi:poi-ooxml:5.2.4@jar"
        simpleName="org.apache.poi:poi-ooxml"/>
    <dependency
        name="org.apache.poi:poi:5.2.4@jar"
        simpleName="org.apache.poi:poi"/>
    <dependency
        name="com.jakewharton.timber:timber:5.0.1@aar"
        simpleName="com.jakewharton.timber:timber"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.profileinstaller:profileinstaller:1.3.1@aar"
        simpleName="androidx.profileinstaller:profileinstaller"/>
    <dependency
        name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
        simpleName="androidx.resourceinspection:resourceinspection-annotation"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.sqlite:sqlite-framework:2.4.0@aar"
        simpleName="androidx.sqlite:sqlite-framework"/>
    <dependency
        name="androidx.sqlite:sqlite:2.4.0@aar"
        simpleName="androidx.sqlite:sqlite"/>
    <dependency
        name="com.github.bumptech.glide:gifdecoder:4.16.0@aar"
        simpleName="com.github.bumptech.glide:gifdecoder"/>
    <dependency
        name="androidx.exifinterface:exifinterface:1.3.6@aar"
        simpleName="androidx.exifinterface:exifinterface"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="androidx.tracing:tracing:1.0.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="androidx.collection:collection-ktx:1.1.0@jar"
        simpleName="androidx.collection:collection-ktx"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection:1.1.0@jar"
        simpleName="androidx.collection:collection"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.concurrent:concurrent-futures:1.1.0@jar"
        simpleName="androidx.concurrent:concurrent-futures"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.documentfile:documentfile:1.0.0@aar"
        simpleName="androidx.documentfile:documentfile"/>
    <dependency
        name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
        simpleName="androidx.localbroadcastmanager:localbroadcastmanager"/>
    <dependency
        name="androidx.print:print:1.0.0@aar"
        simpleName="androidx.print:print"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.0@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.6.0@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="com.squareup.okio:okio-jvm:3.6.0@jar"
        simpleName="com.squareup.okio:okio-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="com.google.errorprone:error_prone_annotations:2.15.0@jar"
        simpleName="com.google.errorprone:error_prone_annotations"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout-core:1.0.4@jar"
        simpleName="androidx.constraintlayout:constraintlayout-core"/>
    <dependency
        name="com.itextpdf:bouncy-castle-connector:8.0.2@jar"
        simpleName="com.itextpdf:bouncy-castle-connector"/>
    <dependency
        name="com.itextpdf:commons:8.0.2@jar"
        simpleName="com.itextpdf:commons"/>
    <dependency
        name="com.itextpdf:font-asian:8.0.2@jar"
        simpleName="com.itextpdf:font-asian"/>
    <dependency
        name="com.itextpdf:hyph:8.0.2@jar"
        simpleName="com.itextpdf:hyph"/>
    <dependency
        name="org.slf4j:slf4j-api:1.7.36@jar"
        simpleName="org.slf4j:slf4j-api"/>
    <dependency
        name="com.github.bumptech.glide:disklrucache:4.16.0@jar"
        simpleName="com.github.bumptech.glide:disklrucache"/>
    <dependency
        name="com.github.bumptech.glide:annotations:4.16.0@jar"
        simpleName="com.github.bumptech.glide:annotations"/>
    <dependency
        name="org.apache.commons:commons-text:1.10.0@jar"
        simpleName="org.apache.commons:commons-text"/>
    <dependency
        name="org.apache.commons:commons-lang3:3.12.0@jar"
        simpleName="org.apache.commons:commons-lang3"/>
    <dependency
        name="commons-beanutils:commons-beanutils:1.9.4@jar"
        simpleName="commons-beanutils:commons-beanutils"/>
    <dependency
        name="org.apache.commons:commons-collections4:4.4@jar"
        simpleName="org.apache.commons:commons-collections4"/>
    <dependency
        name="commons-codec:commons-codec:1.16.0@jar"
        simpleName="commons-codec:commons-codec"/>
    <dependency
        name="org.apache.commons:commons-math3:3.6.1@jar"
        simpleName="org.apache.commons:commons-math3"/>
    <dependency
        name="commons-io:commons-io:2.13.0@jar"
        simpleName="commons-io:commons-io"/>
    <dependency
        name="com.zaxxer:SparseBitSet:1.3@jar"
        simpleName="com.zaxxer:SparseBitSet"/>
    <dependency
        name="org.apache.poi:poi-ooxml-lite:5.2.4@jar"
        simpleName="org.apache.poi:poi-ooxml-lite"/>
    <dependency
        name="org.apache.xmlbeans:xmlbeans:5.1.1@jar"
        simpleName="org.apache.xmlbeans:xmlbeans"/>
    <dependency
        name="org.apache.logging.log4j:log4j-api:2.20.0@jar"
        simpleName="org.apache.logging.log4j:log4j-api"/>
    <dependency
        name="org.apache.commons:commons-compress:1.24.0@jar"
        simpleName="org.apache.commons:commons-compress"/>
    <dependency
        name="com.github.virtuald:curvesapi:1.08@jar"
        simpleName="com.github.virtuald:curvesapi"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="com.google.guava:listenablefuture:1.0@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-common:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-common"/>
    <dependency
        name="commons-logging:commons-logging:1.2@jar"
        simpleName="commons-logging:commons-logging"/>
    <dependency
        name="commons-collections:commons-collections:3.2.2@jar"
        simpleName="commons-collections:commons-collections"/>
  </package>
</dependencies>
