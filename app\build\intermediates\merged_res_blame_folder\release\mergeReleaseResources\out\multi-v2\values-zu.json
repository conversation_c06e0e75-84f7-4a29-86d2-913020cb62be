{"logs": [{"outputFile": "com.mikrotik.cardgenerator.app-mergeReleaseResources-57:/values-zu/values-zu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\69e8a0e0d964fb1e9e45715fb440dd3e\\transformed\\material-1.12.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,271,349,426,503,597,685,797,923,1004,1075,1142,1245,1320,1383,1475,1546,1611,1678,1750,1822,1876,1997,2056,2120,2174,2251,2383,2468,2545,2635,2715,2796,2945,3032,3115,3257,3349,3427,3483,3541,3607,3679,3756,3827,3910,3990,4069,4144,4223,4327,4417,4490,4584,4681,4755,4828,4927,4982,5066,5134,5222,5311,5373,5437,5500,5571,5680,5791,5894,6002,6062,6124,6206,6289,6365", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,77,76,76,93,87,111,125,80,70,66,102,74,62,91,70,64,66,71,71,53,120,58,63,53,76,131,84,76,89,79,80,148,86,82,141,91,77,55,57,65,71,76,70,82,79,78,74,78,103,89,72,93,96,73,72,98,54,83,67,87,88,61,63,62,70,108,110,102,107,59,61,81,82,75,82", "endOffsets": "266,344,421,498,592,680,792,918,999,1070,1137,1240,1315,1378,1470,1541,1606,1673,1745,1817,1871,1992,2051,2115,2169,2246,2378,2463,2540,2630,2710,2791,2940,3027,3110,3252,3344,3422,3478,3536,3602,3674,3751,3822,3905,3985,4064,4139,4218,4322,4412,4485,4579,4676,4750,4823,4922,4977,5061,5129,5217,5306,5368,5432,5495,5566,5675,5786,5889,5997,6057,6119,6201,6284,6360,6443"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,113,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3007,3085,3162,3239,3333,4151,4263,4389,4470,4541,4608,4711,4786,4849,4941,5012,5077,5144,5216,5288,5342,5463,5522,5586,5640,5717,5849,5934,6011,6101,6181,6262,6411,6498,6581,6723,6815,6893,6949,7007,7073,7145,7222,7293,7376,7456,7535,7610,7689,7793,7883,7956,8050,8147,8221,8294,8393,8448,8532,8600,8688,8777,8839,8903,8966,9037,9146,9257,9360,9468,9528,9824,9988,10071,10147", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,113,115,116,117", "endColumns": "12,77,76,76,93,87,111,125,80,70,66,102,74,62,91,70,64,66,71,71,53,120,58,63,53,76,131,84,76,89,79,80,148,86,82,141,91,77,55,57,65,71,76,70,82,79,78,74,78,103,89,72,93,96,73,72,98,54,83,67,87,88,61,63,62,70,108,110,102,107,59,61,81,82,75,82", "endOffsets": "316,3080,3157,3234,3328,3416,4258,4384,4465,4536,4603,4706,4781,4844,4936,5007,5072,5139,5211,5283,5337,5458,5517,5581,5635,5712,5844,5929,6006,6096,6176,6257,6406,6493,6576,6718,6810,6888,6944,7002,7068,7140,7217,7288,7371,7451,7530,7605,7684,7788,7878,7951,8045,8142,8216,8289,8388,8443,8527,8595,8683,8772,8834,8898,8961,9032,9141,9252,9355,9463,9523,9585,9901,10066,10142,10225"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\412d4fb45e796eeac261181f9a418e93\\transformed\\appcompat-1.7.1\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,320,432,520,623,738,817,894,985,1078,1173,1267,1367,1460,1555,1649,1740,1833,1914,2018,2121,2219,2326,2433,2538,2695,2791", "endColumns": "107,106,111,87,102,114,78,76,90,92,94,93,99,92,94,93,90,92,80,103,102,97,106,106,104,156,95,81", "endOffsets": "208,315,427,515,618,733,812,889,980,1073,1168,1262,1362,1455,1550,1644,1735,1828,1909,2013,2116,2214,2321,2428,2533,2690,2786,2868"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "321,429,536,648,736,839,954,1033,1110,1201,1294,1389,1483,1583,1676,1771,1865,1956,2049,2130,2234,2337,2435,2542,2649,2754,2911,9906", "endColumns": "107,106,111,87,102,114,78,76,90,92,94,93,99,92,94,93,90,92,80,103,102,97,106,106,104,156,95,81", "endOffsets": "424,531,643,731,834,949,1028,1105,1196,1289,1384,1478,1578,1671,1766,1860,1951,2044,2125,2229,2332,2430,2537,2644,2749,2906,3002,9983"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\476525ada1d35df1ee329f5bd94fbe69\\transformed\\core-1.13.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,257,356,459,565,672,785", "endColumns": "97,103,98,102,105,106,112,100", "endOffsets": "148,252,351,454,560,667,780,881"}, "to": {"startLines": "38,39,40,41,42,43,44,118", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3421,3519,3623,3722,3825,3931,4038,10230", "endColumns": "97,103,98,102,105,106,112,100", "endOffsets": "3514,3618,3717,3820,3926,4033,4146,10326"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fb7148cc8318b09aaa0d36d8e3e2b12c\\transformed\\navigation-ui-2.7.6\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,168", "endColumns": "112,120", "endOffsets": "163,284"}, "to": {"startLines": "111,112", "startColumns": "4,4", "startOffsets": "9590,9703", "endColumns": "112,120", "endOffsets": "9698,9819"}}]}]}