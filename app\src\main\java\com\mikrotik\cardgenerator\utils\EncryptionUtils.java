package com.mikrotik.cardgenerator.utils;

import android.content.Context;
import android.util.Base64;
import androidx.security.crypto.EncryptedSharedPreferences;
import androidx.security.crypto.MasterKey;
import timber.log.Timber;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.security.SecureRandom;

/**
 * أدوات التشفير للبيانات الحساسة
 */
public class EncryptionUtils {
    
    private static final String ALGORITHM = "AES";
    private static final String TRANSFORMATION = "AES/ECB/PKCS5Padding";
    private static final String ENCRYPTED_PREFS_NAME = "mikrotik_encrypted_prefs";
    private static final String KEY_ALIAS = "mikrotik_master_key";
    
    private Context context;
    private MasterKey masterKey;
    
    public EncryptionUtils(Context context) {
        this.context = context;
        initializeMasterKey();
    }
    
    /**
     * إعداد المفتاح الرئيسي
     */
    private void initializeMasterKey() {
        try {
            masterKey = new MasterKey.Builder(context, KEY_ALIAS)
                    .setKeyScheme(MasterKey.KeyScheme.AES256_GCM)
                    .build();
        } catch (Exception e) {
            Timber.e(e, "خطأ في إعداد المفتاح الرئيسي");
        }
    }
    
    /**
     * تشفير النص
     */
    public String encrypt(String plainText) {
        try {
            if (plainText == null || plainText.isEmpty()) {
                return "";
            }
            
            SecretKey secretKey = generateSecretKey();
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey);
            
            byte[] encryptedBytes = cipher.doFinal(plainText.getBytes("UTF-8"));
            return Base64.encodeToString(encryptedBytes, Base64.DEFAULT);
            
        } catch (Exception e) {
            Timber.e(e, "خطأ في تشفير النص");
            return plainText; // إرجاع النص الأصلي في حالة الخطأ
        }
    }
    
    /**
     * فك تشفير النص
     */
    public String decrypt(String encryptedText) {
        try {
            if (encryptedText == null || encryptedText.isEmpty()) {
                return "";
            }
            
            SecretKey secretKey = generateSecretKey();
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.DECRYPT_MODE, secretKey);
            
            byte[] decodedBytes = Base64.decode(encryptedText, Base64.DEFAULT);
            byte[] decryptedBytes = cipher.doFinal(decodedBytes);
            
            return new String(decryptedBytes, "UTF-8");
            
        } catch (Exception e) {
            Timber.e(e, "خطأ في فك تشفير النص");
            return encryptedText; // إرجاع النص المشفر في حالة الخطأ
        }
    }
    
    /**
     * توليد مفتاح سري
     */
    private SecretKey generateSecretKey() throws Exception {
        // استخدام مفتاح ثابت مشتق من معرف التطبيق
        String keyString = "MikroTikCardGenerator2024!@#$";
        byte[] keyBytes = keyString.getBytes("UTF-8");
        
        // التأكد من أن المفتاح 32 بايت (256 بت)
        byte[] key = new byte[32];
        System.arraycopy(keyBytes, 0, key, 0, Math.min(keyBytes.length, 32));
        
        return new SecretKeySpec(key, ALGORITHM);
    }
    
    /**
     * تشفير كلمة المرور
     */
    public String encryptPassword(String password) {
        return encrypt(password);
    }
    
    /**
     * فك تشفير كلمة المرور
     */
    public String decryptPassword(String encryptedPassword) {
        return decrypt(encryptedPassword);
    }
    
    /**
     * حفظ بيانات مشفرة في SharedPreferences
     */
    public void saveEncryptedData(String key, String value) {
        try {
            EncryptedSharedPreferences encryptedPrefs = EncryptedSharedPreferences.create(
                    context,
                    ENCRYPTED_PREFS_NAME,
                    masterKey,
                    EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
                    EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
            );
            
            encryptedPrefs.edit().putString(key, value).apply();
            
        } catch (Exception e) {
            Timber.e(e, "خطأ في حفظ البيانات المشفرة");
        }
    }
    
    /**
     * قراءة بيانات مشفرة من SharedPreferences
     */
    public String getEncryptedData(String key, String defaultValue) {
        try {
            EncryptedSharedPreferences encryptedPrefs = EncryptedSharedPreferences.create(
                    context,
                    ENCRYPTED_PREFS_NAME,
                    masterKey,
                    EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
                    EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
            );
            
            return encryptedPrefs.getString(key, defaultValue);
            
        } catch (Exception e) {
            Timber.e(e, "خطأ في قراءة البيانات المشفرة");
            return defaultValue;
        }
    }
    
    /**
     * توليد كلمة مرور عشوائية
     */
    public String generateRandomPassword(int length) {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        SecureRandom random = new SecureRandom();
        StringBuilder password = new StringBuilder();
        
        for (int i = 0; i < length; i++) {
            password.append(chars.charAt(random.nextInt(chars.length())));
        }
        
        return password.toString();
    }
    
    /**
     * توليد اسم مستخدم عشوائي
     */
    public String generateRandomUsername(String prefix, String suffix, int length) {
        String chars = "0123456789";
        SecureRandom random = new SecureRandom();
        StringBuilder username = new StringBuilder();
        
        if (prefix != null && !prefix.isEmpty()) {
            username.append(prefix);
        }
        
        for (int i = 0; i < length; i++) {
            username.append(chars.charAt(random.nextInt(chars.length())));
        }
        
        if (suffix != null && !suffix.isEmpty()) {
            username.append(suffix);
        }
        
        return username.toString();
    }
}
