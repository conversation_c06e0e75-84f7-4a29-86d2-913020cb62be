package com.mikrotik.cardgenerator.ui;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import androidx.cardview.widget.CardView;
import com.mikrotik.cardgenerator.R;
import com.mikrotik.cardgenerator.MikroTikApplication;
import timber.log.Timber;

/**
 * الشاشة الرئيسية - اختيار نوع النظام
 */
public class MainActivity extends AppCompatActivity {
    
    private CardView userManagerCard;
    private CardView hotspotCard;
    private Button settingsButton;
    private Button aboutButton;
    private TextView appVersionText;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        
        initializeViews();
        setupClickListeners();
        setupUI();
        
        Timber.i("تم فتح الشاشة الرئيسية");
    }
    
    /**
     * إعداد المكونات
     */
    private void initializeViews() {
        userManagerCard = findViewById(R.id.card_user_manager);
        hotspotCard = findViewById(R.id.card_hotspot);
        settingsButton = findViewById(R.id.btn_settings);
        aboutButton = findViewById(R.id.btn_about);
        appVersionText = findViewById(R.id.tv_app_version);
    }
    
    /**
     * إعداد مستمعي النقر
     */
    private void setupClickListeners() {
        userManagerCard.setOnClickListener(v -> {
            Timber.i("تم اختيار User Manager");
            openCardGenerator("user_manager");
        });
        
        hotspotCard.setOnClickListener(v -> {
            Timber.i("تم اختيار Hotspot");
            openCardGenerator("hotspot");
        });
        
        settingsButton.setOnClickListener(v -> {
            Intent intent = new Intent(this, ConnectionSettingsActivity.class);
            startActivity(intent);
        });
        
        aboutButton.setOnClickListener(v -> {
            showAboutDialog();
        });
    }
    
    /**
     * إعداد واجهة المستخدم
     */
    private void setupUI() {
        // عرض إصدار التطبيق
        try {
            String versionName = getPackageManager()
                    .getPackageInfo(getPackageName(), 0).versionName;
            appVersionText.setText("الإصدار " + versionName);
        } catch (Exception e) {
            appVersionText.setText("الإصدار 2.0");
        }
        
        // إعداد العنوان
        if (getSupportActionBar() != null) {
            getSupportActionBar().setTitle("مولد كروت MikroTik");
        }
    }
    
    /**
     * فتح شاشة توليد الكروت
     */
    private void openCardGenerator(String systemType) {
        Intent intent = new Intent(this, CardGeneratorActivity.class);
        intent.putExtra("system_type", systemType);
        startActivity(intent);
    }
    
    /**
     * عرض نافذة حول التطبيق
     */
    private void showAboutDialog() {
        androidx.appcompat.app.AlertDialog.Builder builder = 
                new androidx.appcompat.app.AlertDialog.Builder(this);
        
        builder.setTitle("حول التطبيق");
        builder.setMessage(
                "مولد كروت وسكريبتات MikroTik\n" +
                "الإصدار 2.0\n\n" +
                "برنامج شامل لتوليد كروت الإنترنت وإدارة المستخدمين\n" +
                "في أجهزة MikroTik RouterOS\n\n" +
                "الميزات:\n" +
                "• دعم User Manager و Hotspot\n" +
                "• توليد كروت PDF قابلة للطباعة\n" +
                "• إدارة قاعدة البيانات\n" +
                "• نسخ احتياطية تلقائية\n" +
                "• تصدير واستيراد البيانات\n" +
                "• واجهة عربية سهلة الاستخدام\n\n" +
                "تطوير: فريق التطوير"
        );
        builder.setPositiveButton("موافق", null);
        builder.show();
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        
        // تحديث حالة الاتصال أو أي معلومات أخرى
        updateConnectionStatus();
    }
    
    /**
     * تحديث حالة الاتصال
     */
    private void updateConnectionStatus() {
        // يمكن إضافة منطق لعرض حالة الاتصال الحالية
        MikroTikApplication app = MikroTikApplication.getInstance();
        String lastConnection = app.getSetting("last_connection_ip", "غير محدد");
        
        // يمكن عرض هذه المعلومات في واجهة المستخدم
        Timber.d("آخر اتصال: " + lastConnection);
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        Timber.i("تم إغلاق الشاشة الرئيسية");
    }
}
