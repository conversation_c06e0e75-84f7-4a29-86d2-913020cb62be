// Generated by view binder compiler. Do not edit!
package com.mikrotik.cardgenerator.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.mikrotik.cardgenerator.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMainBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final Button btnAbout;

  @NonNull
  public final Button btnSettings;

  @NonNull
  public final CardView cardHotspot;

  @NonNull
  public final CardView cardUserManager;

  @NonNull
  public final TextView tvAppVersion;

  private ActivityMainBinding(@NonNull ScrollView rootView, @NonNull Button btnAbout,
      @NonNull Button btnSettings, @NonNull CardView cardHotspot, @NonNull CardView cardUserManager,
      @NonNull TextView tvAppVersion) {
    this.rootView = rootView;
    this.btnAbout = btnAbout;
    this.btnSettings = btnSettings;
    this.cardHotspot = cardHotspot;
    this.cardUserManager = cardUserManager;
    this.tvAppVersion = tvAppVersion;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_about;
      Button btnAbout = ViewBindings.findChildViewById(rootView, id);
      if (btnAbout == null) {
        break missingId;
      }

      id = R.id.btn_settings;
      Button btnSettings = ViewBindings.findChildViewById(rootView, id);
      if (btnSettings == null) {
        break missingId;
      }

      id = R.id.card_hotspot;
      CardView cardHotspot = ViewBindings.findChildViewById(rootView, id);
      if (cardHotspot == null) {
        break missingId;
      }

      id = R.id.card_user_manager;
      CardView cardUserManager = ViewBindings.findChildViewById(rootView, id);
      if (cardUserManager == null) {
        break missingId;
      }

      id = R.id.tv_app_version;
      TextView tvAppVersion = ViewBindings.findChildViewById(rootView, id);
      if (tvAppVersion == null) {
        break missingId;
      }

      return new ActivityMainBinding((ScrollView) rootView, btnAbout, btnSettings, cardHotspot,
          cardUserManager, tvAppVersion);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
