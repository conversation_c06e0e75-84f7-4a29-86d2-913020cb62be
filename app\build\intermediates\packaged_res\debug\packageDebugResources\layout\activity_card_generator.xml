<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_color"
    tools:context=".ui.CardGeneratorActivity">

    <!-- التبويبات -->
    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tab_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/surface_color"
        app:tabTextColor="@color/text_secondary"
        app:tabSelectedTextColor="@color/primary_color"
        app:tabIndicatorColor="@color/primary_color" />

    <!-- شريط التقدم -->
    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="match_parent"
        android:layout_height="4dp"
        android:visibility="gone"
        style="@style/Widget.Material3.LinearProgressIndicator" />

    <!-- المحتوى الرئيسي -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- كارت الاتصال -->
            <androidx.cardview.widget.CardView
                android:id="@+id/card_connection"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="🔗 إعدادات الاتصال"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/primary_color"
                        android:layout_marginBottom="16dp" />

                    <!-- عنوان IP -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/ip_address_hint"
                        style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/edit_ip_address"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="text" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- المنفذ -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/port_hint"
                        style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/edit_port"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="number" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- اسم المستخدم -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/username_hint"
                        style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/edit_username"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="text" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- كلمة المرور -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/password_hint"
                        style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/edit_password"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textPassword" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- حالة الاتصال -->
                    <TextView
                        android:id="@+id/tv_connection_status"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/status_disconnected"
                        android:textSize="14sp"
                        android:textColor="@color/error_color"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="16dp" />

                    <!-- زر الاتصال -->
                    <Button
                        android:id="@+id/btn_connect"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/connect_button"
                        style="@style/Widget.Material3.Button" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- كارت التوليد -->
            <androidx.cardview.widget.CardView
                android:id="@+id/card_generation"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:visibility="gone"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="⚙️ إعدادات التوليد"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/primary_color"
                        android:layout_marginBottom="16dp" />

                    <!-- البادئة والنهاية -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginEnd="8dp"
                            android:hint="@string/prefix_hint"
                            style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/edit_prefix"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="text" />

                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="8dp"
                            android:hint="@string/suffix_hint"
                            style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/edit_suffix"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="text" />

                        </com.google.android.material.textfield.TextInputLayout>

                    </LinearLayout>

                    <!-- العدد والطول -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginEnd="8dp"
                            android:hint="@string/count_hint"
                            style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/edit_count"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="number" />

                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="8dp"
                            android:hint="@string/length_hint"
                            style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/edit_length"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="number" />

                        </com.google.android.material.textfield.TextInputLayout>

                    </LinearLayout>

                    <!-- نوع البيانات -->
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="نوع البيانات:"
                        android:textSize="16sp"
                        android:textColor="@color/text_primary"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="4dp" />

                    <Spinner
                        android:id="@+id/spinner_credential_type"
                        android:layout_width="match_parent"
                        android:layout_height="48dp"
                        android:layout_marginBottom="16dp" />

                    <!-- البروفايل -->
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="البروفايل:"
                        android:textSize="16sp"
                        android:textColor="@color/text_primary"
                        android:layout_marginBottom="4dp" />

                    <Spinner
                        android:id="@+id/spinner_profile"
                        android:layout_width="match_parent"
                        android:layout_height="48dp"
                        android:layout_marginBottom="16dp" />

                    <!-- التعليق -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/comment_hint"
                        style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/edit_comment"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="text" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- الموقع والسعر -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginEnd="8dp"
                            android:hint="@string/location_hint"
                            style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/edit_location"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="text" />

                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="8dp"
                            android:hint="@string/price_hint"
                            style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/edit_price"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="text" />

                        </com.google.android.material.textfield.TextInputLayout>

                    </LinearLayout>

                    <!-- حقول Hotspot -->
                    <LinearLayout
                        android:id="@+id/layout_hotspot_fields"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:visibility="gone">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="إعدادات Hotspot:"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="@color/secondary_color"
                            android:layout_marginTop="16dp"
                            android:layout_marginBottom="8dp" />

                        <!-- حد البيانات -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <com.google.android.material.textfield.TextInputLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:layout_marginEnd="8dp"
                                android:hint="حد البيانات"
                                style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

                                <com.google.android.material.textfield.TextInputEditText
                                    android:id="@+id/edit_limit_bytes"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:inputType="number" />

                            </com.google.android.material.textfield.TextInputLayout>

                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:layout_marginStart="8dp"
                                android:orientation="vertical">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="الوحدة:"
                                    android:textSize="14sp"
                                    android:textColor="@color/text_secondary"
                                    android:layout_marginBottom="4dp" />

                                <Spinner
                                    android:id="@+id/spinner_limit_unit"
                                    android:layout_width="match_parent"
                                    android:layout_height="48dp" />

                            </LinearLayout>

                        </LinearLayout>

                        <!-- عدد الأيام -->
                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="عدد الأيام"
                            style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/edit_days"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="number" />

                        </com.google.android.material.textfield.TextInputLayout>

                    </LinearLayout>

                    <!-- زر التوليد -->
                    <Button
                        android:id="@+id/btn_generate"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/generate_button"
                        android:layout_marginTop="16dp"
                        style="@style/Widget.Material3.Button" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- كارت المعاينة -->
            <androidx.cardview.widget.CardView
                android:id="@+id/card_preview"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="👁️ معاينة النتائج"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/primary_color"
                        android:layout_marginBottom="16dp" />

                    <!-- عداد النتائج -->
                    <TextView
                        android:id="@+id/tv_generated_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="لم يتم التوليد بعد"
                        android:textSize="14sp"
                        android:textColor="@color/text_secondary"
                        android:layout_marginBottom="16dp" />

                    <!-- قائمة النتائج -->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recycler_credentials"
                        android:layout_width="match_parent"
                        android:layout_height="300dp"
                        android:layout_marginBottom="16dp" />

                    <!-- أزرار العمليات -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <Button
                            android:id="@+id/btn_save"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="@string/save_button"
                            android:layout_marginBottom="8dp"
                            style="@style/Widget.Material3.Button" />

                        <Button
                            android:id="@+id/btn_export_pdf"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="@string/export_button"
                            android:layout_marginBottom="8dp"
                            style="@style/Widget.Material3.Button.OutlinedButton" />

                        <Button
                            android:id="@+id/btn_send_to_mikrotik"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="📤 إرسال لـ MikroTik"
                            style="@style/Widget.Material3.Button.OutlinedButton" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </ScrollView>

</LinearLayout>
