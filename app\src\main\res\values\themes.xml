<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Base.Theme.MikroTikCardGenerator" parent="Theme.Material3.DayNight">
        <!-- Customize your light theme here. -->
        <item name="colorPrimary">@color/primary_color</item>
        <item name="colorPrimaryDark">@color/primary_dark</item>
        <item name="colorAccent">@color/accent_color</item>
        <item name="colorSecondary">@color/secondary_color</item>
        <item name="android:colorBackground">@color/background_color</item>
        <item name="colorSurface">@color/surface_color</item>
        <item name="colorOnPrimary">@color/text_on_primary</item>
        <item name="colorOnSurface">@color/text_primary</item>
        <item name="android:textColorPrimary">@color/text_primary</item>
        <item name="android:textColorSecondary">@color/text_secondary</item>
        <item name="android:textColorHint">@color/text_hint</item>

        <!-- دعم RTL للعربية -->
        <item name="android:layoutDirection">rtl</item>
        <item name="android:textDirection">rtl</item>

        <!-- خط افتراضي -->
        <item name="android:fontFamily">sans-serif</item>
    </style>

    <style name="Theme.MikroTikCardGenerator" parent="Base.Theme.MikroTikCardGenerator" />

    <!-- للتوافق مع النظام القديم -->
    <style name="Theme.MyApplication" parent="Theme.MikroTikCardGenerator" />
</resources>