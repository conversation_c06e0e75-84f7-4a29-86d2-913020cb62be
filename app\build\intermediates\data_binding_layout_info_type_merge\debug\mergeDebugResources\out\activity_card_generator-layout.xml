<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_card_generator" modulePackage="com.mikrotik.cardgenerator" filePath="app\src\main\res\layout\activity_card_generator.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_card_generator_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="512" endOffset="14"/></Target><Target id="@+id/tab_layout" view="com.google.android.material.tabs.TabLayout"><Expressions/><location startLine="11" startOffset="4" endLine="18" endOffset="54"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="21" startOffset="4" endLine="26" endOffset="65"/></Target><Target id="@+id/card_connection" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="41" startOffset="12" endLine="145" endOffset="47"/></Target><Target id="@+id/edit_ip_address" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="71" startOffset="24" endLine="75" endOffset="54"/></Target><Target id="@+id/edit_port" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="86" startOffset="24" endLine="90" endOffset="56"/></Target><Target id="@+id/edit_username" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="101" startOffset="24" endLine="105" endOffset="54"/></Target><Target id="@+id/edit_password" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="116" startOffset="24" endLine="120" endOffset="62"/></Target><Target id="@+id/tv_connection_status" view="TextView"><Expressions/><location startLine="125" startOffset="20" endLine="133" endOffset="60"/></Target><Target id="@+id/btn_connect" view="Button"><Expressions/><location startLine="136" startOffset="20" endLine="141" endOffset="64"/></Target><Target id="@+id/card_generation" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="148" startOffset="12" endLine="430" endOffset="47"/></Target><Target id="@+id/edit_prefix" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="186" startOffset="28" endLine="190" endOffset="58"/></Target><Target id="@+id/edit_suffix" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="202" startOffset="28" endLine="206" endOffset="58"/></Target><Target id="@+id/edit_count" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="226" startOffset="28" endLine="230" endOffset="60"/></Target><Target id="@+id/edit_length" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="242" startOffset="28" endLine="246" endOffset="60"/></Target><Target id="@+id/spinner_credential_type" view="Spinner"><Expressions/><location startLine="262" startOffset="20" endLine="266" endOffset="60"/></Target><Target id="@+id/spinner_profile" view="Spinner"><Expressions/><location startLine="277" startOffset="20" endLine="281" endOffset="60"/></Target><Target id="@+id/edit_comment" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="290" startOffset="24" endLine="294" endOffset="54"/></Target><Target id="@+id/edit_location" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="312" startOffset="28" endLine="316" endOffset="58"/></Target><Target id="@+id/edit_price" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="328" startOffset="28" endLine="332" endOffset="58"/></Target><Target id="@+id/layout_hotspot_fields" view="LinearLayout"><Expressions/><location startLine="339" startOffset="20" endLine="417" endOffset="34"/></Target><Target id="@+id/edit_limit_bytes" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="370" startOffset="32" endLine="374" endOffset="64"/></Target><Target id="@+id/spinner_limit_unit" view="Spinner"><Expressions/><location startLine="393" startOffset="32" endLine="396" endOffset="66"/></Target><Target id="@+id/edit_days" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="409" startOffset="28" endLine="413" endOffset="60"/></Target><Target id="@+id/btn_generate" view="Button"><Expressions/><location startLine="420" startOffset="20" endLine="426" endOffset="64"/></Target><Target id="@+id/card_preview" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="433" startOffset="12" endLine="506" endOffset="47"/></Target><Target id="@+id/tv_generated_count" view="TextView"><Expressions/><location startLine="457" startOffset="20" endLine="464" endOffset="60"/></Target><Target id="@+id/recycler_credentials" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="467" startOffset="20" endLine="471" endOffset="60"/></Target><Target id="@+id/btn_save" view="Button"><Expressions/><location startLine="479" startOffset="24" endLine="485" endOffset="68"/></Target><Target id="@+id/btn_export_pdf" view="Button"><Expressions/><location startLine="487" startOffset="24" endLine="493" endOffset="83"/></Target><Target id="@+id/btn_send_to_mikrotik" view="Button"><Expressions/><location startLine="495" startOffset="24" endLine="500" endOffset="83"/></Target></Targets></Layout>